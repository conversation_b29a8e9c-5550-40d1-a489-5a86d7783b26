// 原有代码
import cn.leancloud.json.JSON;

public class BaiduAccessTokenParser {
    public BaiduAccessToken parse(String responseString) {
        return JSON.parseObject(responseString, BaiduAccessToken.class);
    }
}

// 修改后代码
import com.squareup.moshi.JsonAdapter;
import com.squareup.moshi.Moshi;

public class BaiduAccessTokenParser {
    private final Moshi moshi = new Moshi.Builder().build();

    public BaiduAccessToken parse(String responseString) {
        JsonAdapter<BaiduAccessToken> jsonAdapter = moshi.adapter(BaiduAccessToken.class);
        try {
            return jsonAdapter.fromJson(responseString);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse JSON", e);
        }
    }
}