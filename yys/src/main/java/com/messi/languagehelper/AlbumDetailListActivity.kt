package com.messi.languagehelper

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.messi.languagehelper.adapter.RcAlbumDetailListAdapter
import com.messi.languagehelper.bean.RespoData
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.HistoryData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.box.CantoneseAlbums
import com.messi.languagehelper.databinding.ReadingBySubjectActivityBinding
import com.messi.languagehelper.service.PlayerService
import com.messi.languagehelper.util.*
import com.messi.languagehelper.viewmodels.AlbumListViewModel
import com.squareup.moshi.Moshi
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class AlbumDetailListActivity : BaseActivity() {

    private lateinit var mAdapter: RcAlbumDetailListAdapter
    private var mReadingSubject: CantoneseAlbums? = null
    private var objectId: String? = null
    private lateinit var mLinearLayoutManager: LinearLayoutManager
    private lateinit var binding: ReadingBySubjectActivityBinding
    private val viewModel: AlbumListViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ReadingBySubjectActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        registerBroadcast()
        initViews()
    }

    private fun initViews() {
        mReadingSubject = intent.getParcelableExtra(KeyUtil.ParcelableData)
        if (mReadingSubject == null) {
            finish()
            return
        }
        BoxHelper.saveOrGetStatus(mReadingSubject)
        objectId = mReadingSubject!!.objectId
        viewModel.repo.orderBy = mReadingSubject!!.order
        viewModel.repo.code = mReadingSubject!!.code.toString()
        initSwipeRefresh()
        initClick()
        initCollectedButton()
        setListOnScrollListener()
        mAdapter = RcAlbumDetailListAdapter(this, viewModel.readingList, mReadingSubject!!)
        mAdapter.setItems(viewModel.readingList)
        mAdapter.footer = Any()
        hideFooterview()
        mLinearLayoutManager = LinearLayoutManager(this)
        binding.listview.layoutManager = mLinearLayoutManager
        binding.listview.addItemDecoration(
            HorizontalDividerItemDecoration.Builder(this)
                .colorResId(R.color.text_tint)
                .sizeResId(R.dimen.list_divider_size)
                .marginResId(R.dimen.padding_40, R.dimen.padding_10)
                .build()
        )
        binding.listview.adapter = mAdapter
        setOrderByImg()
        setData()
        initViewModel()
        loadData()
    }

    private fun initClick() {
        binding.playAll.setOnClickListener {
            LogUtil.DefalutLog("bid:${mReadingSubject!!.bid},albumId:${PlayerUtil.albumId}")
            if (PlayerUtil.isPlaying() && !TextUtils.isEmpty(mReadingSubject!!.bid) &&
                mReadingSubject!!.bid == PlayerUtil.albumId) {
                PlayerUtil.pause()
            } else {
                toDetailActivity(mReadingSubject!!.lastPlayOid)
            }
            checkPlayState()
        }
        binding.collectBtn.setOnClickListener {
            collectedOrUncollected()
        }
        binding.btnSort.setOnClickListener {
            if (viewModel.repo.orderBy == KSettings.OrderDes) {
                viewModel.repo.orderBy = KSettings.OrderAsc
            } else {
                viewModel.repo.orderBy = KSettings.OrderDes
            }
            setOrderByImg()
            viewModel.sort()
            mReadingSubject!!.order = viewModel.repo.orderBy
            BoxHelper.save(mReadingSubject)
        }
    }

    private fun setData() {
        if (mReadingSubject!!.lastPlayOid != 0 && mReadingSubject!!.lastPlayOid < mReadingSubject!!.total) {
            binding.playAll.text = getString(R.string.title_play_continue)
        } else {
            binding.playAll.text = getString(R.string.title_play_all)
        }
        if (!TextUtils.isEmpty(mReadingSubject!!.img)) {
            binding.itemImg.setImageURI(mReadingSubject!!.img)
        } else {
            binding.itemImg.setImageResource(ColorUtil.getRadomColor())
        }
        binding.itemSign.text = mReadingSubject!!.source_name
        binding.trackInfo.text = mReadingSubject!!.tag
        binding.trackTitle.text = mReadingSubject!!.name
        if (mReadingSubject!!.total > 0) {
            binding.pageCount.text = "${mReadingSubject!!.total} 集"
        } else {
            binding.pageCount.visibility = View.GONE
        }
    }

    private fun loadData() {
        var skip = 0
        viewModel.isFirstLoad = true
        if (mReadingSubject!!.lastPlayOid != 0 && mReadingSubject!!.lastPlayOid < mReadingSubject!!.total) {
            if (viewModel.repo.orderBy == KSettings.OrderAsc) {
                if (mReadingSubject!!.total > Setings.video_list && mReadingSubject!!.lastPlayOid > 15) {
                    skip = mReadingSubject!!.lastPlayOid - 3
                    if ((mReadingSubject!!.total - mReadingSubject!!.lastPlayOid) < 10) {
                        skip = mReadingSubject!!.total - Setings.video_list
                    }
                }
            } else {
                if (mReadingSubject!!.total > Setings.video_list &&
                    (mReadingSubject!!.total - mReadingSubject!!.lastPlayOid) > 15) {
                    skip = (mReadingSubject!!.total - mReadingSubject!!.lastPlayOid) - 3
                    if (mReadingSubject!!.lastPlayOid < 10) {
                        skip = mReadingSubject!!.total - Setings.video_list
                    }
                }
            }
        }
        LogUtil.DefalutLog("AlbumDetailListActivity-loadData-skip:${skip}")
        viewModel.initData(skip)
    }

    fun setListOnScrollListener() {
        binding.listview.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val visible = mLinearLayoutManager.childCount
                val total = mLinearLayoutManager.itemCount
                val firstVisibleItem = mLinearLayoutManager.findFirstCompletelyVisibleItemPosition()
                if (visible + firstVisibleItem >= total) {
                    LogUtil.DefalutLog("AlbumDetailListActivity-setListOnScrollListener")
                    viewModel.loadMore()
                }
            }
        })
    }

    override fun updateUI(music_action: String) {
        when (music_action) {
            PlayerService.action_loading -> {
                showProgressbar()
            }
            PlayerService.action_finish_loading -> {
                hideProgressbar()
            }
            else -> {
                refreshOid()
            }
        }
    }

    private fun hideFooterview() {
        mAdapter.hideFooter()
    }

    private fun showFooterview() {
        mAdapter.showFooter()
    }

    override fun onSwipeRefreshLayoutRefresh() {
        LogUtil.DefalutLog("---onSwipeRefreshLayoutRefresh---")
        hideFooterview()
        viewModel.refresh(mReadingSubject!!.total)
    }

    private fun setOrderByImg() {
        if (viewModel.repo.orderBy == KSettings.OrderDes) {
            binding.imgSort.setImageResource(R.drawable.main_album_sort_desc)
        } else {
            binding.imgSort.setImageResource(R.drawable.main_album_sort_asc)
        }
    }

    private fun initCollectedButton() {
        if (BoxHelper.isCollected(objectId)) {
            binding.volumeImg.setImageResource(R.drawable.ic_collected_white)
            binding.volumeImg.tag = true
        } else {
            binding.volumeImg.setImageResource(R.drawable.ic_uncollected_white)
            binding.volumeImg.tag = false
        }
    }

    private fun collectedOrUncollected() {
        val tag = !(binding.volumeImg.tag as Boolean)
        binding.volumeImg.tag = tag
        if (mReadingSubject != null) {
            if (tag) {
                binding.volumeImg.setImageResource(R.drawable.ic_collected_white)
                ToastUtil.diaplayMesShort(this, "已收藏")
            } else {
                binding.volumeImg.setImageResource(R.drawable.ic_uncollected_white)
                ToastUtil.diaplayMesShort(this, "取消收藏")
            }
            viewModel.collectData(tag, mReadingSubject!!)
        }
    }

    private fun initViewModel() {
        viewModel.respoData.observe(this) { data: RespoData<*>? -> onDataChange(data) }
        viewModel.isShowProgressBar.observe(this) { isShow: Boolean -> isShowProgressBar(isShow) }
    }

    private fun onDataChange(data: RespoData<*>?) {
        LogUtil.DefalutLog("ViewModel---onDataChange---")
        if (data != null) {
            if (data.code > 0) {
                if (data.code == 1) {
                    if (::mAdapter.isInitialized) {
                        mAdapter.notifyItemRangeInserted(data.positionStart, data.itemCount)
                    }
                } else if (data.code == 4) {
                    if (::mAdapter.isInitialized) {
                        mAdapter.notifyItemRangeInserted(0, data.itemCount)
                        mLinearLayoutManager.scrollToPositionWithOffset(0, 0)
                    }
                }
                if (viewModel.isFirstLoad && mReadingSubject!!.lastPlayOid > 0) {
                    viewModel.isFirstLoad = false
                    var sindex = 0
                    for ((index,item) in viewModel.readingList.withIndex()) {
                        if (item.order == mReadingSubject!!.lastPlayOid) {
                            sindex = index
                        }
                    }
                    mLinearLayoutManager.scrollToPositionWithOffset(sindex, 0)
                }
            } else {
                ToastUtil.diaplayMesShort(this, data.errStr)
            }
            if (data.isHideFooter) {
                hideFooterview()
            } else {
                showFooterview()
            }
        } else {
            ToastUtil.diaplayMesShort(this, "网络异常，请检查网络连接。")
        }
    }

    private fun isShowProgressBar(isShow: Boolean) {
        if (isShow) {
            showProgressbar()
        } else {
            hideProgressbar()
            onSwipeRefreshLayoutFinish()
        }
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch {
            delay(500)
            refreshOid()
            checkPlayState()
        }

    }

    private fun refreshOid(){
        checkPlayState()
        if (PlayerUtil.isPlaying() && mReadingSubject!!.bid == PlayerUtil.albumId) {
            mReadingSubject!!.lastPlayOid = PlayerUtil.order
        }
        if (::mAdapter.isInitialized) {
            for (item in viewModel.readingList) {
                BoxHelper.saveOrGetStatus(item)
            }
            mAdapter.notifyDataSetChanged()
        }
    }

    private fun checkPlayState(){
        if (PlayerUtil.isPlaying() && mReadingSubject!!.bid == PlayerUtil.albumId) {
            binding.playAll.text = getString(R.string.title_pause)
        } else {
            if (mReadingSubject!!.lastPlayOid != 0 && mReadingSubject!!.lastPlayOid < mReadingSubject!!.total) {
                binding.playAll.text = getString(R.string.title_play_continue)
            } else {
                binding.playAll.text = getString(R.string.title_play_all)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterBroadcast()
    }

    private fun toDetailActivity(orderId: Int) {
        var position = 0
        if (orderId > 0) {
            for(item in viewModel.readingList){
                if (item.order == orderId){
                    position = viewModel.readingList.indexOf(item)
                    break
                }
            }
        }
        LogUtil.DefalutLog("position:${position}")
        if(viewModel.readingList.size > position) {
            val item = viewModel.readingList[position]
            val intent = Intent()
            PlayerUtil.initList(viewModel.readingList, position, 0, mReadingSubject!!.order)
            intent.setClass(this, PlayListActivity::class.java)
            startActivity(intent)
            overridePendingTransition(R.anim.zoom_in_from_bottom, R.anim.stay)
            updateStatus(item)
        }
    }

    private fun updateStatus(item: NDetail) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                delay(100)
                item.status = "1"
                mReadingSubject!!.lastPlayOid = item.order
                BoxHelper.updateState(item)
                BoxHelper.save(mReadingSubject)
                val moshi = Moshi.Builder().build()
                val jsonAdapter = moshi.adapter(CantoneseAlbums::class.java)
                val json = jsonAdapter.toJson(mReadingSubject)
                val history = HistoryData()
                history.objectId = mReadingSubject!!.objectId
                history.type = AVOUtil.SubjectList.SubjectList
                history.json = json
                history.updateTime = System.currentTimeMillis()
                BoxHelper.insertOrUpdate(history)
            }
        } catch (e: Exception) {
            LogUtil.DefalutLog("updateStatus---Exception")
            e.printStackTrace()
        }
    }
}