package com.messi.languagehelper

import android.content.SharedPreferences
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.adModel.AdDTModel
import com.messi.languagehelper.adModel.AdPool
import com.messi.languagehelper.bean.AdData
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.databinding.CompositionDetailActivityBinding
import com.messi.languagehelper.util.*
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ReadingDetailActivity : BaseActivity() {

    private var mAVObject: NDetail? = null
    private var mAVObjects: List<NDetail>? = null
    private lateinit var mSharedPreferences: SharedPreferences
    private var index = 0
    var adMLData = MutableLiveData<AdData>()
    private var mAdData: AdData? = null
    private lateinit var binding: CompositionDetailActivityBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = CompositionDetailActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        initData()
        setData()
        guide()
    }

    private fun initData() {
        mSharedPreferences = Setings.getSharedPreferences(this)
        index = intent.getIntExtra(KeyUtil.IndexKey, 0)
        val data = Setings.dataMap[KeyUtil.DataMapKey]
        Setings.dataMap.clear()
        if (data is List<*>) {
            mAVObjects = data as List<NDetail>?
            if (mAVObjects != null && mAVObjects!!.size > index) {
                mAVObject = mAVObjects!![index]
            }
        }
    }

    private fun setData() {
        if (mAVObject == null) {
            finish()
            return
        }
        binding.title.text = mAVObject!!.title
        setActionBarTitle(mAVObject!!.title)
        binding.scrollview.scrollTo(0, 0)
        if (!TextUtils.isEmpty(mAVObject!!.img_url)) {
            binding.adImg.visibility = View.VISIBLE
            binding.adImg.setImageURI(mAVObject!!.img_url)
        }
        TextHandlerUtil.handlerText(this, binding.content, mAVObject!!.content)
        AdPool.mAdDTModelRef.getAdData(adMLData)
        adMLData.observe(this) {
            LogUtil.DefalutLog("adMLData.observe(this)")
            mAdData = it
            AdDTModel.showAd(this, it, binding.adLayout)
        }
        if ("text" == mAVObject!!.type) {
            binding.playerLayout.visibility = View.GONE
        }
        if (TextUtils.isEmpty(mAVObject!!.status)) {
            mAVObject!!.status = "1"
            BoxHelper.updateState(mAVObject)
        }
        if (BoxHelper.isCollected(mAVObject!!.object_id)) {
            mAVObject!!.isCollected = "1"
        } else {
            mAVObject!!.isCollected = ""
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.composition, menu)
        return true
    }

    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        if (menu.size() > 1) {
            setMenuIcon(menu.getItem(1))
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        super.onOptionsItemSelected(item)
        when (item.itemId) {
            R.id.action_share -> copyOrshare(0)
            R.id.action_collected -> {
                setCollectStatus()
                setMenuIcon(item)
                updateData()
            }
        }
        return true
    }

    private fun setCollectStatus() {
        if (TextUtils.isEmpty(mAVObject!!.isCollected)) {
            mAVObject!!.isCollected = "1"
        } else {
            mAVObject!!.isCollected = ""
        }
    }

    private fun updateData() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                if (mAVObject != null) {
                    if (!TextUtils.isEmpty(mAVObject!!.isCollected)) {
                        val cdata = CollectedData()
                        cdata.objectId = mAVObject!!.object_id
                        cdata.name = mAVObject!!.title
                        cdata.type = AVOUtil.Reading.Reading
                        val moshi = Moshi.Builder().build()
                        val adapter = moshi.adapter(NDetail::class.java)
                        cdata.json = adapter.toJson(mAVObject)
                        BoxHelper.insert(cdata)
                    } else {
                        val cdata = CollectedData()
                        cdata.objectId = mAVObject!!.object_id
                        BoxHelper.remove(cdata)
                    }
                    LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun setMenuIcon(item: MenuItem?) {
        if (item != null && mAVObject != null) {
            if (TextUtils.isEmpty(mAVObject!!.isCollected)) {
                item.icon = getDrawable(R.drawable.ic_uncollected_white)
            } else {
                item.icon = getDrawable(R.drawable.ic_collected_white)
            }
        }
    }

    private fun copyOrshare(i: Int) {
        val sb = StringBuilder()
        sb.append(mAVObject!!.title)
        sb.append("\n")
        sb.append(mAVObject!!.content)
        if (i == 0) {
            Setings.share(this, sb.toString())
        } else {
            Setings.copy(this, sb.toString())
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mAdData?.destroy()
    }

    private fun guide() {
        if (!mSharedPreferences.getBoolean(KeyUtil.isReadingDetailGuideShow, false)) {
            val builder = AlertDialog.Builder(this, R.style.Theme_AppCompat_Light_Dialog_Alert)
            builder.setTitle("")
            builder.setMessage("点击英文单词即可查询词意。")
            builder.setPositiveButton("确认", null)
            val dialog = builder.create()
            dialog.show()
            Setings.saveSharedPreferences(
                mSharedPreferences,
                KeyUtil.isReadingDetailGuideShow,
                true
            )
        }
    }
}