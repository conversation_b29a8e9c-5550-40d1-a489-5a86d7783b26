package com.messi.languagehelper

import android.Manifest
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.messi.languagehelper.adapter.RcAiChatAdapter
import com.messi.languagehelper.bean.AiResult
import com.messi.languagehelper.box.AiEntity
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.databinding.AiChatActivityBinding
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.http.UICallback
import com.messi.languagehelper.util.*
import com.messi.languagehelper.util.KViewUtil.showRecordImgAnimation
import com.messi.languagehelper.util.MyPlayer.startByUrl
import com.messi.languagehelper.util.MyPlayer.stop
import com.messi.languagehelper.viewmodels.AIChatModel
import com.squareup.moshi.Moshi
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jsoup.Jsoup
import java.util.*

class AiChatActivity : BaseActivity() {

    private lateinit var mLinearLayoutManager: LinearLayoutManager
    lateinit var mAdapter: RcAiChatAdapter
    private lateinit var sp: SharedPreferences
    private var isSayHello = false
    private lateinit var binding: AiChatActivityBinding
    private val mViewModel: AIChatModel by viewModels()

    private val requestPermission =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                lifecycleScope.launch {
                    showIatDialog()
                }
            } else {
                onShowRationale()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = AiChatActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        initSwipeRefresh()
        initData()
//        sayHi()
    }

    private fun initData() {
        setActionBarTitle(resources.getString(R.string.title_ai_chat))
        sp = KSettings.getSP(this)
        initViewClick()
        mViewModel.beans.addAll(BoxHelper.getAiEntityList(AiUtil.Ai_Acobot))
        mAdapter = RcAiChatAdapter()
        mAdapter.setItems(mViewModel.beans)
        mLinearLayoutManager = LinearLayoutManager(this)
        if (mViewModel.beans.size > 4) {
            mLinearLayoutManager.stackFromEnd = true
        }
        binding.contentLv.layoutManager = mLinearLayoutManager
        binding.contentLv.adapter = mAdapter
        setSpeakLanguageTv()
        if (sp.getBoolean(KeyUtil.IsAiChatPlayVoice, true)) {
            binding.volumeImg.setImageResource(R.drawable.ic_volume_on)
        } else {
            binding.volumeImg.setImageResource(R.drawable.ic_volume_off)
        }
        if (sp.getBoolean(KeyUtil.IsAiChatShowKeybordLayout, true)) {
            showKeybordLayout()
        } else {
            showMicLayout()
        }
        binding.keybordLayout.requestFocus()

        AsrHelper.asrXbkjResult.observe(this) {
            hideProgressbar()
            finishRecord()
            if (it == null) {
                NetworkUtil.showNetworkStatus(this)
            }
            if (it.isNotEmpty()){
                binding.inputEt.append(it)
                submit()
            }
        }
        AsrHelper.asrXbkjVolume.observe(this) {
            showRecordImgAnimation(it, binding.recordAnimImg)
        }
    }

    private fun sayHi() {
        lifecycleScope.launch {
            delay(300L)
            if (!isSayHello) {
                isSayHello = true
                val sayHi = "say hello"
                if (mViewModel.beans.size > 0) {
                    val mAiEntity = mViewModel.beans[mViewModel.beans.size - 1]
                    if (sayHi != mAiEntity.content) {
                        requestData(sayHi)
                    }
                } else {
                    requestData(sayHi)
                }
            }
        }

    }

    override fun onSwipeRefreshLayoutRefresh() {
        if (mViewModel.beans.size > 0) {
            val list = BoxHelper.getAiEntityList(mViewModel.beans[0].id, AiUtil.Ai_Acobot)
            if (list.isNotEmpty()) {
                mViewModel.beans.addAll(0, list)
                mAdapter.notifyDataSetChanged()
                binding.contentLv.scrollToPosition(list.size)
            }
        }
        onSwipeRefreshLayoutFinish()
    }

    private fun initViewClick() {
        binding.volumeBtn.setOnClickListener {
            val isPlay = !sp.getBoolean(KeyUtil.IsAiChatPlayVoice, true)
            Setings.saveSharedPreferences(
                sp,
                KeyUtil.IsAiChatPlayVoice,
                isPlay
            )
            if (isPlay) {
                binding.volumeImg.setImageResource(R.drawable.ic_volume_on)
            } else {
                binding.volumeImg.setImageResource(R.drawable.ic_volume_off)
            }
        }
        binding.submitBtnCover.setOnClickListener { submit() }
        binding.inputTypeLayout.setOnClickListener { changeInputType() }
        binding.voiceBtnCover.setOnClickListener { requestPermission.launch(Manifest.permission.RECORD_AUDIO) }
        binding.speakLanguageLayout.setOnClickListener { changeSpeakLanguage() }
        binding.deleteBtn.setOnClickListener { clear_all() }
    }

    private fun clear_all() {
        mViewModel.beans.clear()
        mAdapter.notifyDataSetChanged()
        BoxHelper.deleteAiEntity(AiUtil.Ai_Acobot)
    }

    private fun submit() {
        if (!TextUtils.isEmpty(binding.inputEt.text.toString().trim())) {
            val mAiEntity = AiEntity()
            mAiEntity.role = AiUtil.Role_User
            mAiEntity.content_video_id = System.currentTimeMillis().toString()
            mAiEntity.content = binding.inputEt.text.toString().trim()
            mAiEntity.content_type = AiUtil.Content_Type_Text
            mAiEntity.entity_type = AiUtil.Entity_Type_Chat
            mAiEntity.ai_type = AiUtil.Ai_Acobot
            addEntity(mViewModel.beans.size, mAiEntity)
            mViewModel.chat()
//            binding.contentLv.scrollToPosition(mViewModel.beans.size - 1)
//            requestData(binding.inputEt.text.toString().trim { it <= ' ' })
            binding.inputEt.setText("")
            BoxHelper.insertOrUpdate(mAiEntity)
            hideIME(binding.inputEt)
        }
    }

    private fun addEntity(position: Int, entity: AiEntity) {
        mViewModel.beans.add(position, entity)
        mAdapter.notifyItemRangeInserted(position, 1)
        val lastItemPosition: Int = mAdapter.itemCount - 1
        binding.contentLv.scrollToPosition(lastItemPosition)
    }

    fun showIatDialog() {
        if (!AsrHelper.isListening()) {
            binding.recordLayout.visibility = View.VISIBLE
            binding.inputEt.setText("")
            binding.voiceBtn.text = this.resources.getText(R.string.click_and_finish)
            val speaker = sp.getString(KeyUtil.AiChatUserSelectLanguage, XFUtil.VoiceEngineEN)
            AsrHelper.startListening(this, speaker!!)
        } else {
            finishRecord()
            AsrHelper.stopListening()
            showProgressbar()
        }
    }

    /**
     * finish record
     */
    private fun finishRecord() {
        binding.recordLayout.visibility = View.GONE
        binding.recordAnimImg.setBackgroundResource(R.drawable.speak_voice_1)
        binding.voiceBtn.text = this.resources.getText(R.string.click_and_speak)
    }

    private fun changeSpeakLanguage() {
        if (sp.getString(
                KeyUtil.AiChatUserSelectLanguage,
                XFUtil.VoiceEngineEN
            ) == XFUtil.VoiceEngineMD
        ) {
            Setings.saveSharedPreferences(
                sp,
                KeyUtil.AiChatUserSelectLanguage,
                XFUtil.VoiceEngineEN
            )
            ToastUtil.diaplayMesShort(this, this.resources.getString(R.string.speak_english))
        } else {
            Setings.saveSharedPreferences(
                sp,
                KeyUtil.AiChatUserSelectLanguage,
                XFUtil.VoiceEngineMD
            )
            ToastUtil.diaplayMesShort(this, this.resources.getString(R.string.speak_chinese))
        }
        setSpeakLanguageTv()
        AVAnalytics.onEvent(this, "tab3_ai_changelan")
    }

    private fun setSpeakLanguageTv() {
        binding.speakLanguageTv.text = XFUtil.getVoiceEngineText(
            sp.getString(
                KeyUtil.AiChatUserSelectLanguage,
                XFUtil.VoiceEngineEN
            )
        )
    }

    private fun changeInputType() {
        if (binding.keybordLayout.isShown) {
            showMicLayout()
            Setings.saveSharedPreferences(sp, KeyUtil.IsAiChatShowKeybordLayout, false)
            hideIME(binding.inputEt)
        } else {
            showKeybordLayout()
            Setings.saveSharedPreferences(sp, KeyUtil.IsAiChatShowKeybordLayout, true)
            showIME()
            binding.inputEt.requestFocus()
        }
    }

    private fun showKeybordLayout() {
        binding.inputTypeBtn.setImageDrawable(getDrawable(R.drawable.ic_mic))
        binding.keybordLayout.visibility = View.VISIBLE
        binding.micLayout.visibility = View.GONE
    }

    private fun showMicLayout() {
        binding.inputTypeBtn.setImageDrawable(getDrawable(R.drawable.ic_keybord_btn))
        binding.keybordLayout.visibility = View.GONE
        binding.micLayout.visibility = View.VISIBLE
    }

    private fun requestData(msg: String) {
        showProgressbar()
        val url = Setings.AiBrainUrl + Setings.getDeviceID(this) + "&msg=" + msg
        LanguagehelperHttpClient.get(url, object : UICallback(this) {
            override fun onResponsed(responseString: String) {
                try {
                    LogUtil.DefalutLog(responseString)
                    if (JsonParser.isJson(responseString)) {
                        val moshi = Moshi.Builder().build()
                        val jsonAdapter = moshi.adapter(AiResult::class.java)
                        val mAiResult = jsonAdapter.fromJson(responseString)
                        addAiResult(mAiResult)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onFailured() {
                ToastUtil.diaplayMesShort(
                    this@AiChatActivity,
                    <EMAIL>(R.string.network_error)
                )
            }

            override fun onFinished() {
                onSwipeRefreshLayoutFinish()
                hideProgressbar()
            }
        })
    }

    private fun addAiResult(mAiResult: AiResult?) {
        lifecycleScope.launch {
            mAiResult?.let {
                val mAiEntity = parseResult(mAiResult.cnt)
                addEntity(mViewModel.beans.size, mAiEntity)
                binding.contentLv.scrollToPosition(mViewModel.beans.size - 1)
                if (sp.getBoolean(KeyUtil.IsAiChatPlayVoice, true)) {
                    playVideo(mAiEntity)
                }
                BoxHelper.insertOrUpdate(mAiEntity)
            }
        }
    }

    private fun parseResult(result: String): AiEntity {
        val mAiEntity = AiEntity()
        mAiEntity.role = AiUtil.Role_Machine
        mAiEntity.entity_type = AiUtil.Entity_Type_Chat
        mAiEntity.ai_type = AiUtil.Ai_Acobot
        mAiEntity.content_video_id = System.currentTimeMillis().toString()
        if (result.contains("<a href=")) {
            val doc = Jsoup.parse(result)
            val element = doc.select("a").first()
            if (element != null) {
                mAiEntity.content = element.text()
            }
            mAiEntity.content_type = AiUtil.Content_Type_Link
            if (element != null) {
                mAiEntity.link = element.attr("href")
            }
        } else {
            mAiEntity.content = result
            mAiEntity.content_type = AiUtil.Content_Type_Text
        }
        return mAiEntity
    }

    private fun playVideo(mAiEntity: AiEntity) {
        startByUrl(mAiEntity.content)
    }

    @Deprecated("This method has been deprecated in favor of using the\n      {@link OnBackPressedDispatcher} via {@link #getOnBackPressedDispatcher()}.\n      The OnBackPressedDispatcher controls how back button events are dispatched\n      to one or more {@link OnBackPressedCallback} objects.")
    override fun onBackPressed() {
        hideIME(binding.inputEt)
        super.onBackPressed()
    }

    fun onShowRationale() {
        ToastUtil.diaplayMesShort(this, "需要授权才能使用。")
    }

    override fun onStop() {
        super.onStop()
        stop()
        AsrHelper.stopListening()
    }
}