package com.messi.languagehelper.repositories

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.messi.languagehelper.bean.BoutiquesBean
import com.messi.languagehelper.bean.RespoData
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.CantoneseAlbums
import com.messi.languagehelper.box.HistoryData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.util.AVOUtil
import com.messi.languagehelper.util.LogUtil
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class HistoryListRepository {

    var mRespoData = MutableLiveData<RespoData<String>>()
    private val pageSize = 50
    var list: MutableList<HistoryData> = ArrayList()
    var skip = 0
    var hasMore = true
    var loading = false

    fun getDataList(isRefresh: Boolean) {
        if (isRefresh) {
            skip = 0
            list.clear()
            hasMore = true
        }
        LogUtil.DefalutLog("hasMore:$hasMore")
        if (!loading && hasMore) {
            CoroutineScope(Dispatchers.IO).launch {
                loading = true
                val mResult = loadData()
                mRespoData.postValue(mResult)
                loading = false
            }
        }
    }

    suspend fun loadData(): RespoData<String> =
        withContext(Dispatchers.IO) {
            LogUtil.DefalutLog("loadData")
            val result = RespoData<String>()
            result.code = 0
            val datas = BoxHelper.getHistoryDataList(skip, pageSize)
            if (datas.isNotEmpty()) {
                initDataObject(datas)
                result.code = 1
                result.positionStart = list.size
                result.itemCount = datas.size
                LogUtil.DefalutLog("size:${datas.size}")
                list.addAll(datas)
                skip += pageSize
            }
            hasMore = datas.size == pageSize
            result.isHideFooter = (list.size <= 0)
            result
        }

    private fun initDataObject(datas: List<HistoryData>) {
        try {
            for (item in datas) {
                LogUtil.DefalutLog("item.json:${item.type}")
                if (!TextUtils.isEmpty(item.json)) {
                    when (item.type) {
                        AVOUtil.Reading.Reading -> {
                            val moshi = Moshi.Builder().build()
                            val jsonAdapter = moshi.adapter(NDetail::class.java)
                            val mAVObject = jsonAdapter.fromJson(item.json)
                            item.mReading = mAVObject
                        }
                        AVOUtil.Boutiques.Boutiques -> {
                            val moshi = Moshi.Builder().build()
                            val jsonAdapter = moshi.adapter(BoutiquesBean::class.java)
                            val mBoutiquesBean = jsonAdapter.fromJson(item.json)
                            item.mBoutiquesBean = mBoutiquesBean
                        }
                        AVOUtil.SubjectList.SubjectList -> {
                            val moshi = Moshi.Builder().build()
                            val jsonAdapter = moshi.adapter(CantoneseAlbums::class.java)
                            val mReadingSubject = jsonAdapter.fromJson(item.json)
                            item.mReadingSubject = mReadingSubject
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}