package com.messi.languagehelper.adapter

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

import com.google.android.material.color.MaterialColors
import com.messi.languagehelper.*
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.HistoryData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.box.CantoneseAlbums
import com.messi.languagehelper.util.*
import com.squareup.moshi.Moshi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by luli on 10/23/16.
 */
class RcAlbumDetailListItemViewHolder(
    private val context: Activity,
    itemView: View,
    private val avObjects: List<NDetail>,
    val mReadingSubject: CantoneseAlbums
) : RecyclerView.ViewHolder(itemView) {

    private val layout_cover: FrameLayout = itemView.findViewById(R.id.layout_cover)
    private val source_name: TextView = itemView.findViewById(R.id.source_name)
    private val title: TextView = itemView.findViewById(R.id.title)
    private val order: TextView = itemView.findViewById(R.id.order)
    private val play_status: TextView = itemView.findViewById(R.id.play_status)
    private val music_play_img: ImageView = itemView.findViewById(R.id.music_play_img)

    fun render(mAVObject: NDetail) {
        layout_cover.visibility = View.VISIBLE
        music_play_img.visibility = View.VISIBLE
        source_name.visibility = View.GONE
        play_status.text = ""
        order.text = "${mAVObject.order}"
        title.text = mAVObject.title
        if (mAVObject.duration > 0) {
            source_name.visibility = View.VISIBLE
            source_name.text = TimeUtil.getDuration(mAVObject.duration)
        }
        //正在播放当前专辑
        if (PlayerUtil.isPlaying() && mReadingSubject.bid == PlayerUtil.albumId) {
            if (PlayerUtil.isSameMedia(mAVObject)) {
                title.setTextColor(MaterialColors.getColor(context, R.attr.colorPrimary, Color.YELLOW))
                order.setTextColor(MaterialColors.getColor(context, R.attr.colorPrimary, Color.YELLOW))
                play_status.setTextColor(MaterialColors.getColor(context, R.attr.colorPrimary, Color.YELLOW))
                play_status.text = "正在播放"
            } else {
                if (TextUtils.isEmpty(mAVObject.status)) {
                    title.setTextColor(context.resources.getColor(R.color.text_dark))
                    order.setTextColor(context.resources.getColor(R.color.text_black))
                } else {
                    title.setTextColor(context.resources.getColor(R.color.text_grey2))
                    order.setTextColor(context.resources.getColor(R.color.text_grey2))
                }
                play_status.setTextColor(context.resources.getColor(R.color.text_grey))
            }
        } else {
            if (mReadingSubject.lastPlayOid != 0 && mAVObject.order == mReadingSubject.lastPlayOid) {
                title.setTextColor(MaterialColors.getColor(context, R.attr.colorPrimary, Color.YELLOW))
                order.setTextColor(MaterialColors.getColor(context, R.attr.colorPrimary, Color.YELLOW))
                play_status.setTextColor(MaterialColors.getColor(context, R.attr.colorPrimary, Color.YELLOW))
                if (!TextUtils.isEmpty(mAVObject.type) && (mAVObject.type == "video" || mAVObject.type == "audio")) {
                    play_status.text = "上次听到"
                }else {
                    play_status.text = "上次看到"
                }
            } else {
                if (TextUtils.isEmpty(mAVObject.status)) {
                    title.setTextColor(context.resources.getColor(R.color.text_dark))
                    order.setTextColor(context.resources.getColor(R.color.text_black))
                } else {
                    title.setTextColor(context.resources.getColor(R.color.text_grey2))
                    order.setTextColor(context.resources.getColor(R.color.text_grey2))
                }
                play_status.setTextColor(context.resources.getColor(R.color.text_grey))
            }
        }
        if (!TextUtils.isEmpty(mAVObject.type) && (mAVObject.type == "video" || mAVObject.type == "audio")) {
            if (PlayerUtil.isPlaying()) {
                if (PlayerUtil.isSameMedia(mAVObject)) {
                    play_status.text = "正在播放"
                    music_play_img.setImageResource(R.drawable.ic_pause_grey)
                }else {
                    music_play_img.setImageResource(R.drawable.ic_play_grey)
                }
            } else {
                music_play_img.setImageResource(R.drawable.ic_play_grey)
            }
        }else {
            music_play_img.visibility = View.GONE
        }
        layout_cover.setOnClickListener {
            toDetailActivity(mAVObject)
        }
    }

    private fun toDetailActivity(item: NDetail) {
        var position = avObjects.indexOf(item)
        if(avObjects.size < position) {
            position = 0
        }
        val intent = Intent()
        PlayerUtil.initList(avObjects, position)
        intent.setClass(context, PlayListActivity::class.java)
        context.startActivity(intent)
        context.overridePendingTransition(R.anim.zoom_in_from_bottom, R.anim.stay)
        updateStatus(item)
    }

    private fun updateStatus(item: NDetail) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                delay(200)
                item.status = "1"
                mReadingSubject.lastPlayOid = item.order
                BoxHelper.updateState(item)
                BoxHelper.save(mReadingSubject)
                val moshi = Moshi.Builder().build()
                val jsonAdapter = moshi.adapter(CantoneseAlbums::class.java)
                val json = jsonAdapter.toJson(mReadingSubject)
                val history = HistoryData()
                history.objectId = mReadingSubject.objectId
                history.type = AVOUtil.SubjectList.SubjectList
                history.json = json
                history.updateTime = System.currentTimeMillis()
                BoxHelper.insertOrUpdate(history)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}