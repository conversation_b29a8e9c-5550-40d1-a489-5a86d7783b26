package com.messi.languagehelper.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.MutableLiveData
import com.messi.languagehelper.bean.RespoData
import com.messi.languagehelper.repositories.ReadingListRepository
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.messi.languagehelper.box.CantoneseAlbums
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.util.AVOUtil

import com.messi.languagehelper.box.BoxHelper
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.util.KeyUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ReadingListViewModel : ViewModel() {

    private var isLoading: MutableLiveData<Boolean>
    private var mRespoData: MutableLiveData<RespoData<*>>
    private var mMutaCount: MutableLiveData<Int>
    var repo: ReadingListRepository = ReadingListRepository()

    init {
        mRespoData = repo.mRespoData
        isLoading = repo.isLoading
        mMutaCount = repo.mMutaCount
        isLoading.value = false
    }

    fun refresh(skip: Int) {
        repo.setNeedClear(true)
        repo.isHasMore = true
        repo.skip = skip
        repo.getReadingList()
    }

    fun loadData(skip: Int) {
        repo.isHasMore = true
        repo.isAddToHead = true
        repo.skip = skip
        repo.getReadingList()
    }

    fun loadData() {
        repo.getReadingList()
    }

    fun count() {
        repo.count()
    }

    val isShowProgressBar: LiveData<Boolean>
        get() = isLoading
    val readingList: LiveData<RespoData<*>>
        get() = mRespoData
    val count: LiveData<Int>
        get() = mMutaCount

    fun collectData(tag: Boolean, mReadingSubject: CantoneseAlbums) {
        viewModelScope.launch(Dispatchers.IO) {
            val cdata = CollectedData()
            if (tag) {
                cdata.objectId = mReadingSubject.objectId
                cdata.name = mReadingSubject.name
                cdata.type = AVOUtil.SubjectList.SubjectList
                cdata.json = JSON.toJSONString(mReadingSubject)
                BoxHelper.insert(cdata)
            } else {
                cdata.objectId = mReadingSubject.objectId
                BoxHelper.remove(cdata)
            }
            LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
        }
    }
}