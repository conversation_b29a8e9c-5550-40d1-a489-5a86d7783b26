package com.messi.languagehelper.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import cn.leancloud.LCObject

import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.bean.RespoData
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.box.CantoneseAlbums
import com.messi.languagehelper.repositories.AlbumListRepository
import com.messi.languagehelper.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.ArrayList

class AlbumListViewModel : ViewModel() {

    var isLoading = MutableLiveData<Boolean>()
    var mRespoData = MutableLiveData<RespoData<List<NDetail>>>()
    var readingList: MutableList<NDetail> = ArrayList()
    var repo: AlbumListRepository = AlbumListRepository()
    var isHasMore = true
    var loading = false
    var isFirstLoad = false
    var isNeedClear = false
    var mSkip = 0
    var total = 0
    private var lastRequestTime = 0L

    init {
        isLoading.value = false
    }

    fun initData(skip: Int) {
        if (checkIsLoading()) {
            LogUtil.DefalutLog("loading:$loading,skip:$skip,isHasMore:$isHasMore,isNeedClear:$isNeedClear")
            viewModelScope.launch {
                isLoading.value = true
                loading = true
                isHasMore = true
                mSkip = skip
                val results = repo.loadData(mSkip)
                setData(results)
            }
        }
    }

    fun loadMore() {
        if (readingList.isNotEmpty()) {
            LogUtil.DefalutLog("orderby:${repo.orderBy},total:$total,order:${readingList.last().order}")
            if (repo.orderBy == KSettings.OrderAsc) {
                if (readingList.last().order == total) {
                    noMoreData()
                }
            } else {
                if (readingList.last().order == 1) {
                    noMoreData()
                }
            }
        }

        if (checkIsLoading()) {
            viewModelScope.launch {
                isLoading.value = true
                loading = true
                val results = repo.loadData(mSkip)
                setData(results)
            }
        }
    }

    private fun noMoreData(){
        isHasMore = false
        val mData: RespoData<List<NDetail>> = RespoData()
        mData.code=0
        mData.isHideFooter= true
        mRespoData.value = mData
    }

    fun sort() {
        isHasMore = true
        isNeedClear = true
        initData(0)
    }

    fun refresh(total: Int) {
        if (readingList.isNotEmpty()) {
            if (repo.orderBy == KSettings.OrderAsc) {
                if (readingList.first().order == 1) {
                    isLoading.value = false
                    return
                }
            } else {
                if (readingList.first().order == total) {
                    isLoading.value = false
                    return
                }
            }
        }
        isLoading.value = true
        loading = true
        isHasMore = true
        viewModelScope.launch {
            if (readingList.size > 0) {
                val fOrder = readingList[0].order
                LogUtil.DefalutLog("AlbumListRepository-refresh-orderBy:${repo.orderBy}")
                LogUtil.DefalutLog("AlbumListRepository-refresh-fOrder:$fOrder")
                LogUtil.DefalutLog("AlbumListRepository-refresh-total:$total")
                if (repo.orderBy == KSettings.OrderAsc) {
                    val results = repo.loadDataByOrderId(fOrder)
                    addToHeak(results)
                } else {
                    val results = repo.loadDataByOrderId(fOrder)
                    addToHeak(results)
                }
            }else {
                doRefresh()
            }
        }
    }

    private fun doRefresh(){
        LogUtil.DefalutLog("AlbumListRepository-doRefresh")
        viewModelScope.launch {
            isNeedClear = true
            mSkip = 0
            val results = repo.loadData(mSkip)
            setData(results)
        }
    }

    private fun setData(mData: RespoData<List<NDetail>>) {
        if (mData.total > 0) {
            total = mData.total
        }
        if (mData.data != null) {
            if (mData.data!!.isEmpty()) {
                isHasMore = false
            } else {
                if (isNeedClear) {
                    isNeedClear = false
                    readingList.clear()
                }
                mData.positionStart = readingList.size
                mSkip += mData.data!!.size
                readingList.addAll(mData.data!!)
                if (mData.data!!.size == Setings.video_list) {
                    isHasMore = true
                    mData.isHideFooter = false
                } else {
                    isHasMore = false
                    mData.isHideFooter = true
                }
            }
        }
        mRespoData.value = mData
        isLoading.value = false
        loading = false
    }

    private fun addToHeak(mData: RespoData<List<NDetail>>) {
        if (mData.code == 4) {
            readingList.addAll(0, mData.data!!)
        }
        mRespoData.value = mData
        isLoading.value = false
        loading = false
        LogUtil.DefalutLog("AlbumListRepository-addToHeak-itemCount:${mData.itemCount}")
    }

    private fun checkIsLoading(): Boolean{
        val space = System.currentTimeMillis() - lastRequestTime
        lastRequestTime = System.currentTimeMillis()
        return !loading && isHasMore && space > 100
    }

    val isShowProgressBar: LiveData<Boolean>
        get() = isLoading
    val respoData: LiveData<RespoData<List<NDetail>>>
        get() = mRespoData

    fun collectData(tag: Boolean, mReadingSubject: CantoneseAlbums) {
        viewModelScope.launch(Dispatchers.IO) {
            val cdata = CollectedData()
            if (tag) {
                cdata.objectId = mReadingSubject.objectId
                cdata.name = mReadingSubject.name
                cdata.type = AVOUtil.SubjectList.SubjectList
                cdata.json = JSON.toJSONString(mReadingSubject)
                BoxHelper.insert(cdata)
            } else {
                cdata.objectId = mReadingSubject.objectId
                BoxHelper.remove(cdata)
            }
            LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
        }
    }
}