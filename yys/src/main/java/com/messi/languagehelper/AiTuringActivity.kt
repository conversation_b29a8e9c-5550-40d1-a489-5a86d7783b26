package com.messi.languagehelper

import android.Manifest
import android.content.SharedPreferences
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.messi.languagehelper.adapter.RcAiTuringAdapter
import com.messi.languagehelper.bean.AiTuringResult
import com.messi.languagehelper.box.AiEntity
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.databinding.AiTuringActivityBinding
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.http.UICallback
import com.messi.languagehelper.util.*
import com.squareup.moshi.Moshi
import okhttp3.FormBody

class AiTuringActivity : BaseActivity() {

    private lateinit var beans: MutableList<AiEntity>
    private lateinit var mLinearLayoutManager: LinearLayoutManager
    lateinit var mAdapter: RcAiTuringAdapter
    private lateinit var sp: SharedPreferences
    private lateinit var binding: AiTuringActivityBinding

    private val requestPermission =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                showIatDialog()
            } else {
                onShowRationale()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = AiTuringActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        initSwipeRefresh()
        initData()
    }

    private fun initData() {
        setActionBarTitle(resources.getString(R.string.title_tuling_ai))
        initViewClicked()
        sp = Setings.getSharedPreferences(this)
        beans = ArrayList()
        beans.addAll(BoxHelper.getAiEntityList(AiUtil.Ai_Turing))
        mAdapter = RcAiTuringAdapter(this, beans)
        mAdapter.setItems(beans)
        mLinearLayoutManager = LinearLayoutManager(this)
        if (beans.size > 7) {
            mLinearLayoutManager.stackFromEnd = true
        }
        binding.contentLv.layoutManager = mLinearLayoutManager
        binding.contentLv.adapter = mAdapter
        if (sp.getBoolean(KeyUtil.IsAiTuringPlayVoice, true)) {
            binding.volumeImg.setImageResource(R.drawable.ic_volume_on)
        } else {
            binding.volumeImg.setImageResource(R.drawable.ic_volume_off)
        }
        if (KSettings.getSP(this).getBoolean(KeyUtil.IsAiChatShowKeybordLayout, true)) {
            showKeybordLayout()
        } else {
            showMicLayout()
        }
        binding.keybordLayout.requestFocus()
        AsrHelper.asrXbkjVolume.observe(this) {
            KViewUtil.showRecordImgAnimation(it, binding.recordAnimImg)
        }
        AsrHelper.asrXbkjResult.observe(this) {
            hideProgressbar()
            finishRecord()
            if (it == null) {
                finishRecord()
                NetworkUtil.showNetworkStatus(this)
            }
            if (it.isNotEmpty()){
                binding.inputEt.setText(it)
                submit()
            }
        }
    }

    override fun onSwipeRefreshLayoutRefresh() {
        if (beans.isNotEmpty()) {
            val list = BoxHelper.getAiEntityList(beans[0].id, AiUtil.Ai_Turing)
            if (list.isNotEmpty()) {
                beans.addAll(0, list)
                mAdapter.notifyDataSetChanged()
                binding.contentLv.scrollToPosition(list.size)
            }
        }
        onSwipeRefreshLayoutFinish()
    }

    private fun initViewClicked() {
        binding.volumeBtn.setOnClickListener {
            val isPlay = !sp.getBoolean(KeyUtil.IsAiTuringPlayVoice, true)
            Setings.saveSharedPreferences(
                sp,
                KeyUtil.IsAiTuringPlayVoice,
                isPlay
            )
            if (isPlay) {
                binding.volumeImg.setImageResource(R.drawable.ic_volume_on)
            } else {
                binding.volumeImg.setImageResource(R.drawable.ic_volume_off)
            }
        }
        binding.submitBtnCover.setOnClickListener { submit() }
        binding.inputTypeLayout.setOnClickListener { changeInputType() }
        binding.voiceBtnCover.setOnClickListener {
            requestPermission.launch(Manifest.permission.RECORD_AUDIO)
        }
        binding.deleteBtn.setOnClickListener { clear_all() }
    }

    private fun clear_all() {
        beans.clear()
        mAdapter.notifyDataSetChanged()
        BoxHelper.deleteAiEntity(AiUtil.Ai_Turing)
    }

    private fun submit() {
        var input = binding.inputEt.text.toString().trim { it <= ' ' }
        if (!TextUtils.isEmpty(input)) {
            val last = input.substring(input.length - 1)
            if (",.!;:'，。！‘；：".contains(last)) {
                input = input.substring(0, input.length - 1)
            }
            val mAiEntity = AiEntity()
            mAiEntity.role = AiUtil.Role_User
            mAiEntity.content_video_id = System.currentTimeMillis().toString()
            mAiEntity.content = input
            mAiEntity.content_type = AiUtil.Content_Type_Text
            mAiEntity.entity_type = AiUtil.Entity_Type_Chat
            mAiEntity.ai_type = AiUtil.Ai_Turing
            mAdapter.addEntity(beans.size, mAiEntity)
            binding.contentLv.scrollToPosition(beans.size - 1)
            requestData(binding.inputEt.text.toString().trim { it <= ' ' })
            binding.inputEt.setText("")
            BoxHelper.insertOrUpdate(mAiEntity)
        }
    }

    fun showIatDialog() {
        if (!AsrHelper.isListening()) {
            binding.recordLayout.visibility = View.VISIBLE
            binding.inputEt.setText("")
            binding.voiceBtn.text = this.resources.getText(R.string.click_and_finish)
            AsrHelper.startListening(this)
        } else {
            finishRecord()
            AsrHelper.stopListening()
            showProgressbar()
        }
    }

    private fun finishRecord() {
        binding.recordLayout.visibility = View.GONE
        binding.recordAnimImg.setBackgroundResource(R.drawable.speak_voice_1)
        binding.voiceBtn.text = this.resources.getText(R.string.click_and_speak)
    }

    private fun changeInputType() {
        if (binding.keybordLayout.isShown) {
            showMicLayout()
            Setings.saveSharedPreferences(
                KSettings.getSP(this),
                KeyUtil.IsAiChatShowKeybordLayout,
                false
            )
            hideIME(binding.inputEt)
        } else {
            showKeybordLayout()
            Setings.saveSharedPreferences(
                KSettings.getSP(this),
                KeyUtil.IsAiChatShowKeybordLayout,
                true
            )
            showIME()
            binding.inputEt.requestFocus()
        }
    }

    private fun showKeybordLayout() {
        binding.inputTypeBtn.setImageDrawable(getDrawable(R.drawable.ic_mic))
        binding.keybordLayout.visibility = View.VISIBLE
        binding.micLayout.visibility = View.GONE
    }

    private fun showMicLayout() {
        binding.inputTypeBtn.setImageDrawable(getDrawable(R.drawable.ic_keybord_btn))
        binding.keybordLayout.visibility = View.GONE
        binding.micLayout.visibility = View.VISIBLE
    }

    private fun requestData(msg: String) {
        showProgressbar()
        val formBody: FormBody = FormBody.Builder()
            .add("key", Setings.AiTuringApiKey)
            .add("info", msg)
            .add("userid", Setings.getDeviceID(this))
            .build()
        LanguagehelperHttpClient.post(Setings.AiTuringApi, formBody, object : UICallback(this) {
            override fun onResponsed(responseString: String) {
                try {
                    LogUtil.DefalutLog(responseString)
                    if (JsonParser.isJson(responseString)) {
                        val moshi = Moshi.Builder().build()
                        val jsonAdapter = moshi.adapter(AiTuringResult::class.java)
                        val mAiResult = jsonAdapter.fromJson(responseString)
                        addAiResult(mAiResult)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onFailured() {
                ToastUtil.diaplayMesShort(
                    this@AiTuringActivity,
                    <EMAIL>(R.string.network_error)
                )
            }

            override fun onFinished() {
                onSwipeRefreshLayoutFinish()
                hideProgressbar()
            }
        })
    }

    private fun addAiResult(mAiResult: AiTuringResult?) {
        if (mAiResult != null) {
            val mAiEntity = AiEntity()
            mAiEntity.role = AiUtil.Role_Machine
            mAiEntity.entity_type = AiUtil.Entity_Type_Chat
            mAiEntity.ai_type = AiUtil.Ai_Turing
            mAiEntity.content_video_id = System.currentTimeMillis().toString()
            mAiEntity.content = mAiResult.text
            if (TextUtils.isEmpty(mAiResult.url)) {
                mAiEntity.content_type = AiUtil.Content_Type_Text
            } else {
                mAiEntity.link = mAiResult.url
                mAiEntity.content_type = AiUtil.Content_Type_Link
            }
            mAdapter.addEntity(beans.size, mAiEntity)
            binding.contentLv.scrollToPosition(beans.size - 1)
            if (sp.getBoolean(KeyUtil.IsAiTuringPlayVoice, true)) {
                playVideo(mAiEntity)
            }
            BoxHelper.insertOrUpdate(mAiEntity)
        }
    }

    fun playVideo(mAiEntity: AiEntity) {
        val path = SDCardUtil.getDownloadPath(SDCardUtil.sdPath)
        if (TextUtils.isEmpty(mAiEntity.content_video_id)) {
            mAiEntity.content_video_id = MD5.encode(mAiEntity.content)
        }
        val filepath = path + mAiEntity.content_video_id + ".pcm"
        mAiEntity.content_video_path = filepath
    }

    fun onShowRationale() {
        ToastUtil.diaplayMesShort(this, "需要授权才能使用。")
    }
}