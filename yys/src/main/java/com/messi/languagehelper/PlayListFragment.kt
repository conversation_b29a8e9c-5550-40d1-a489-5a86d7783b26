package com.messi.languagehelper

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.SeekBar
import androidx.appcompat.content.res.AppCompatResources.getDrawable
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.facebook.drawee.backends.pipeline.Fresco
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.adModel.AdDTModel
import com.messi.languagehelper.adModel.AdPool
import com.messi.languagehelper.bean.AdData
import com.messi.languagehelper.bean.BoutiquesBean
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.CantoneseAlbums
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.databinding.PlaylistFragmentBinding
import com.messi.languagehelper.service.PlayerService
import com.messi.languagehelper.util.*
import com.messi.languagehelper.util.CountDownType.*
import com.squareup.moshi.Moshi
import kotlinx.coroutines.*

class PlayListFragment : BaseFragment(), SeekBar.OnSeekBarChangeListener {

    lateinit var binding: PlaylistFragmentBinding
    private var scope: CoroutineScope? = null
    private var isActive: Boolean = true
    private var currentItem: NDetail? = null
    private lateinit var timeSheet: BottomSheetBehavior<View>
    private lateinit var speedSheet: BottomSheetBehavior<View>
    private lateinit var playListSheet: BottomSheetBehavior<View>
    var radioGroups = ArrayList<RadioButton>()
    private var isShowAd = false
    private var repeatModel = 0
    var mAdList: MutableList<AdData> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerBroadcast()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = try {
            PlaylistFragmentBinding.inflate(inflater)
        } catch (e: Exception) {
            Fresco.initialize(context?.applicationContext)
            PlaylistFragmentBinding.inflate(inflater)
        }
        init()
        initPlayContent()
        return binding.root
    }

    fun init() {
        addRadioButtons()
        binding.bottomSheetTime.visibility = VISIBLE
        binding.bottomSheetSpeed.visibility = VISIBLE
        binding.bottomSheetPlaylist.visibility = VISIBLE
        timeSheet = BottomSheetBehavior.from(binding.bottomSheetTime)
        speedSheet = BottomSheetBehavior.from(binding.bottomSheetSpeed)
        playListSheet = BottomSheetBehavior.from(binding.bottomSheetPlaylist)
        timeSheet.state = BottomSheetBehavior.STATE_HIDDEN
        speedSheet.state = BottomSheetBehavior.STATE_HIDDEN
        playListSheet.state = BottomSheetBehavior.STATE_HIDDEN
        PlayerUtil.musicServiceConnection?.repeatModel("repeat_model", 100)
        playListSheet.skipCollapsed = false
        binding.seekbar.setOnSeekBarChangeListener(this)
        binding.jiEdtxt.setText(
            KSettings.getSP(requireContext()).getInt(KeyUtil.JiCustomDefault, 3).toString()
        )
        binding.fzEdtxt.setText(
            KSettings.getSP(requireContext()).getInt(KeyUtil.FZCustomDefault, 60).toString()
        )
        PlayerUtil.musicServiceConnection?.repeatModel?.observe(requireActivity()) {
            repeatModel = it
            if (repeatModel == 1) {
                binding.playModelImg.setImageResource(R.drawable.icon_repeat_one)
            } else {
                binding.playModelImg.setImageResource(R.drawable.icon_repeat_all)
            }
        }
        initListener()
        initListView()
        initSpeedRadioButton()
        showText()
        setCountdownNumber()
    }

    private fun initListView() {
        val videoListFragment: Fragment = PlayListMenuListFragment()
        childFragmentManager
            .beginTransaction()
            .add(R.id.pager, videoListFragment)
            .commit()
    }

    private fun initListener() {
        binding.playSpeed.text = getString(R.string.title_speed)
        binding.playbtnLayout.setOnClickListener { playBtn() }
        binding.alarmBtn.setOnClickListener { showAlarmDialog() }
        binding.bottomSheetCloseBtn.setOnClickListener {
            timeSheet.state = BottomSheetBehavior.STATE_HIDDEN
        }
        binding.speedSheetCloseBtn.setOnClickListener {
            speedSheet.state = BottomSheetBehavior.STATE_HIDDEN
        }
        binding.playSpeedLayout.setOnClickListener {
            speedSheet.state = BottomSheetBehavior.STATE_EXPANDED
        }
        binding.playlistSheetCloseBtn.setOnClickListener {
            playListSheet.state = BottomSheetBehavior.STATE_HIDDEN
        }
        binding.playListBtn.setOnClickListener {
            playListSheet.state = BottomSheetBehavior.STATE_EXPANDED
        }
        binding.speedGroup.setOnCheckedChangeListener { _, i ->
            setSpeed(i)
        }
        binding.playPrevious.setOnClickListener {
            PlayerUtil.playPrevious()
        }
        binding.playNext.setOnClickListener {
            val info = PlayerUtil.playNext()
            if (info.isNotEmpty()) {
                ToastUtil.diaplayMesShort(requireContext(), info)
            }
        }
        binding.collectedLayout.setOnClickListener {
            setCollectStatus()
            setCollectIcon()
            updateData()
        }
        binding.playModelLayout.setOnClickListener{
            when (repeatModel) {
                0 -> {
                    repeatModel = 1
                    binding.playModelImg.setImageResource(R.drawable.icon_repeat_one)
                }
                else -> {
                    repeatModel = 0
                    binding.playModelImg.setImageResource(R.drawable.icon_repeat_all)
                }
            }
            PlayerUtil.musicServiceConnection?.repeatModel("repeat_model", repeatModel)
        }
    }

    private fun setCollectStatus() {
        if (currentItem != null) {
            if (TextUtils.isEmpty(currentItem!!.isCollected)) {
                currentItem!!.isCollected = "1"
            } else {
                currentItem!!.isCollected = ""
            }
        }
    }

    private fun playBtn() {
        KSettings.notificationGuide(requireContext())
        if (PlayerUtil.isPlaying()) {
            binding.btnPlay.isSelected = false
            PlayerUtil.pause()
        } else {
            binding.btnPlay.isSelected = true
            PlayerUtil.play()
        }
    }

    private fun showAlarmDialog() {
        onAlarmFinish()
        timeSheet.state = BottomSheetBehavior.STATE_EXPANDED
    }

    private fun startScope() {
        scope = CoroutineScope(Dispatchers.IO)
        scope!!.launch {
            while (isActive) {
                try {
                    activity?.runOnUiThread {
                        setTimeProgressbar()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    <EMAIL> = false
                }
                delay(300)
            }
        }
    }

    private fun initPlayContent() {
        binding.btnPlay.isSelected = PlayerUtil.isPlaying()
    }

    private fun setTimeProgressbar() {
        val currentPosition = PlayerUtil.currentPosition
        val mDuration = PlayerUtil.duration
        if (mDuration > 0) {
            binding.seekbar.max = mDuration
            binding.playTimeDuration.text = TimeUtil.getDuration(mDuration / 1000)
        }
        binding.seekbar.progress = currentPosition
        binding.playTimeCurrent.text = TimeUtil.getDuration(currentPosition / 1000)
    }

    private fun showText() {
        lifecycleScope.launch {
            isShowAd = true
            currentItem = null
            setData()
            showAd()
        }
    }

    private fun setData() {
        binding.seriesTitle.visibility = GONE
        val cItem = PlayerUtil.currentItem
        if (cItem != null) {
            if (currentItem != null && cItem.object_id == currentItem!!.object_id) {
                LogUtil.DefalutLog("setData---cItem.object_id == currentItem!!.object_id")
            } else {
                currentItem = cItem
                binding.title.text = currentItem!!.title
                if (currentItem!!.duration > 0) {
                    binding.playTimeDuration.text = TimeUtil.getDuration(currentItem!!.duration)
                }
                if (BoxHelper.isCollected(cItem.object_id)) {
                    cItem.isCollected = "1"
                } else {
                    cItem.isCollected = ""
                }
                setCollectIcon()
                binding.content.text = StringUtils.fromHtml(currentItem!!.content)
                binding.adLayout.visibility = VISIBLE
                if (!TextUtils.isEmpty(cItem.albumId)) {
                    lifecycleScope.launch {
                        val audioAlbum =
                            CloudAndDatabaseHelper.getReadingSubjectByAlbumId(cItem.albumId)
                        if (audioAlbum != null && !TextUtils.isEmpty(audioAlbum.name)) {
                            binding.seriesTitle.visibility = VISIBLE
                            binding.seriesTitle.text = "专辑：${audioAlbum.name} >"
                            binding.seriesTitle.setOnClickListener {
                                toAlbumActivity(audioAlbum)
                            }
                        }
                    }
                }
                if (!TextUtils.isEmpty(cItem.boutique_code)) {
                    lifecycleScope.launch {
                        val videoAlbum =
                            CloudAndDatabaseHelper.getBoutiqueByCode(cItem.boutique_code)
                        if (videoAlbum != null && !TextUtils.isEmpty(videoAlbum.title)) {
                            binding.seriesTitle.visibility = VISIBLE
                            binding.seriesTitle.text = "系列视频：${videoAlbum.title} >"
                            binding.seriesTitle.setOnClickListener {
                                toVideoActivity(videoAlbum)
                            }
                        }
                    }
                }
                scope?.cancel()
                startScope()
                if (!TextUtils.isEmpty(currentItem!!.img_url)) {
                    binding.adImg.visibility = VISIBLE
                    binding.adImg.setImageURI(currentItem!!.img_url)
                } else {
                    binding.adImg.visibility = GONE
                }
            }
        }
    }

    private fun showAd() {
        val adData = AdPool.mAdDTModelRef.getAdData()
        LogUtil.DefalutLog("adMLData.observe(this):$adData")
        adData?.let {
            mAdList.add(it)
            AdDTModel.showAd(requireActivity(), it, binding.adLayout)
        }
    }

    private fun toAlbumActivity(mReadingSubject: CantoneseAlbums) {
        val intent = Intent(context, AlbumDetailListActivity::class.java)
        intent.putExtra(KeyUtil.ActionbarTitle, mReadingSubject.name)
        intent.putExtra(KeyUtil.ObjectKey, mReadingSubject)
        startActivity(intent)
    }

    private fun toVideoActivity(mBoutiquesBean: BoutiquesBean) {
        val intent = Intent(context, ReadVideoActivity::class.java)
        intent.putExtra(KeyUtil.ObjectKey, mBoutiquesBean)
        intent.putExtra(KeyUtil.StartPosition, PlayerUtil.currentPosition.toLong())
        startActivity(intent)
    }

    private fun setCollectIcon() {
        if (currentItem != null && TextUtils.isEmpty(currentItem!!.isCollected)) {
            binding.collectedImg.setImageDrawable(
                getDrawable(
                    requireContext(),
                    R.drawable.ic_uncollected_grey
                )
            )
        } else {
            binding.collectedImg.setImageDrawable(
                getDrawable(
                    requireContext(),
                    R.drawable.ic_collected_grey
                )
            )
        }
    }

    private fun updateData() {
        lifecycleScope.launch(Dispatchers.IO) {
            if (currentItem != null) {
                if (!TextUtils.isEmpty(currentItem!!.isCollected)) {
                    val cdata = CollectedData()
                    cdata.objectId = currentItem!!.object_id
                    cdata.name = currentItem!!.title
                    cdata.type = AVOUtil.Reading.Reading
                    val moshi = Moshi.Builder().build()
                    val JSON = moshi.adapter(NDetail::class.java)
                    cdata.json = JSON.toJson(currentItem)
                    BoxHelper.insert(cdata)
                } else {
                    val cdata = CollectedData()
                    cdata.objectId = currentItem!!.object_id
                    BoxHelper.remove(cdata)
                }
                LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
            }
        }
    }

    private fun setCountdownNumber() {
        val num = CountDownUtil.getCountdownNumber()
        LogUtil.DefalutLog("---setCountdownNumber---num:${num}")
        if (num > 0) {
            reselect(CountDownUtil.lastSelected)
            binding.alarmTxt.text = num.toString()
        } else {
            binding.alarmTxt.text = ""
        }
        if (CountDownUtil.millisUntilFinished > 1000) {
            reselect(CountDownUtil.lastSelected)
        }
    }

    override fun updateUI(music_action: String) {
        LogUtil.DefalutLog("music_action:$music_action")
        setCountdownNumber()
        when (music_action) {
            PlayerService.action_restart -> {
                binding.btnPlay.isSelected = false
            }
            PlayerService.action_pause -> {
                binding.btnPlay.isSelected = true
                showText()
            }
        }
    }

    fun cancel() {
        isActive = false
        scope?.cancel()
        scope = null
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.DefalutLog("PlayListFragment---onDestroy")
        cancel()
        unregisterBroadcast()
        for (item in mAdList) {
            item.destroy()
        }
        mAdList.clear()
    }

    override fun onProgressChanged(p0: SeekBar?, p1: Int, p2: Boolean) {
    }

    override fun onStartTrackingTouch(p0: SeekBar?) {
        scope?.cancel()
        PlayerUtil.pause()
    }

    override fun onStopTrackingTouch(seekBar: SeekBar?) {
        if (seekBar != null) {
            startScope()
            PlayerUtil.seekTo(seekBar.progress)
            PlayerUtil.play()
        }
    }

    private fun addRadioButtons() {
        radioGroups.add(binding.alarmOff)
        radioGroups.add(binding.alarm10m)
        radioGroups.add(binding.alarm20m)
        radioGroups.add(binding.alarm30m)
        radioGroups.add(binding.alarm45m)
        radioGroups.add(binding.alarm60m)
        radioGroups.add(binding.alarmOne)
        radioGroups.add(binding.alarmTwo)
        radioGroups.add(binding.alarmThree)
        binding.jiEdtxt.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                val txt = p0.toString()
                val result = try {
                    val tem = txt.toInt()
                    if (tem > 0) {
                        tem
                    } else {
                        3
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    3
                }
                KSettings.saveSharedPreferences(KeyUtil.JiCustomDefault, result)
                if (binding.alarmThree.isChecked) {
                    LogUtil.DefalutLog("binding.alarmThree.isChecked:${result}")
                    CountDownUtil.setCountdownNumber(result)
                    setCountdownNumber()
                }
            }
        })
        binding.fzEdtxt.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                val txt = p0.toString()
                val result = try {
                    val tem = txt.toInt()
                    if (tem > 0) {
                        tem
                    } else {
                        60
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    60
                }
                KSettings.saveSharedPreferences(KeyUtil.FZCustomDefault, result)
                if (binding.alarm60m.isChecked) {
                    LogUtil.DefalutLog("binding.alarm60m.isChecked:${result}")
                    CountDownUtil.startCountdown(result.toLong() * 60 * 1000)
                }
            }
        })
        binding.alarmOff.setOnClickListener {
            CountDownUtil.lastSelected = alarmOff
            setAlarm(it as RadioButton)
            closeAlarm()
        }
        binding.alarm10m.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarm10m
            CountDownUtil.startCountdown(10 * 60 * 1000L)
            setAlarm(it as RadioButton)
        }
        binding.alarm20m.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarm20m
            CountDownUtil.startCountdown(20 * 60 * 1000L)
            setAlarm(it as RadioButton)
        }
        binding.alarm30m.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarm30m
            CountDownUtil.startCountdown(30 * 60 * 1000L)
            setAlarm(it as RadioButton)
        }
        binding.alarm45m.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarm45m
            CountDownUtil.startCountdown(45 * 60 * 1000L)
            setAlarm(it as RadioButton)
        }
        binding.alarm60m.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarm60m
            val time = KSettings.getSP(requireContext()).getInt(KeyUtil.FZCustomDefault, 60)
            CountDownUtil.startCountdown(time.toLong() * 60 * 1000)
            setAlarm(it as RadioButton)
        }
        binding.alarmOne.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarmOne
            CountDownUtil.setCountdownNumber(1)
            setAlarm(it as RadioButton)
        }
        binding.alarmTwo.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarmTwo
            CountDownUtil.setCountdownNumber(2)
            setAlarm(it as RadioButton)
        }
        binding.alarmThree.setOnClickListener {
            closeAlarm()
            CountDownUtil.lastSelected = alarmThree
            CountDownUtil.setCountdownNumber(
                KSettings.getSP(requireContext()).getInt(KeyUtil.JiCustomDefault, 3)
            )
            setAlarm(it as RadioButton)
        }
        CountDownUtil.ticker.observe(viewLifecycleOwner) {
            LogUtil.DefalutLog("tick:$it")
            if (it > 1000) {
                binding.alarmTxt.text = TimeUtil.getDuration((it / 1000).toInt())
            } else {
                if (CountDownUtil.getCountdownNumber() == 0) {
                    binding.alarmTxt.text = ""
                }
            }
        }
    }

    private fun closeAlarm() {
        binding.alarmTxt.text = ""
        CountDownUtil.cancelAlarm()
    }

    private fun setAlarm(rbtn: RadioButton) {
        LogUtil.DefalutLog("---reset---")
        reset()
        rbtn.isChecked = true
        setCountdownNumber()
    }

    private fun reset() {
        for (item in radioGroups) {
            item.isChecked = false
        }
    }

    private fun onAlarmFinish() {
        if (CountDownUtil.millisUntilFinished < 100 &&
            CountDownUtil.getCountdownNumber() <= 0
        ) {
            reset()
            binding.alarmOff.isChecked = true
        }
    }

    fun reselect(type: CountDownType) {
        reset()
        when (type) {
            alarmOff -> binding.alarmOff.isChecked = true
            alarm10m -> binding.alarm10m.isChecked = true
            alarm20m -> binding.alarm20m.isChecked = true
            alarm30m -> binding.alarm30m.isChecked = true
            alarm45m -> binding.alarm45m.isChecked = true
            alarm60m -> binding.alarm60m.isChecked = true
            alarmOne -> binding.alarmOne.isChecked = true
            alarmTwo -> binding.alarmTwo.isChecked = true
            alarmThree -> binding.alarmThree.isChecked = true
        }
    }

    private fun setSpeed(i: Int) {
        var speed = 1.0f
        when (i) {
            R.id.speed_03 -> {
                speed = 0.3f
            }
            R.id.speed_04 -> {
                speed = 0.4f
            }
            R.id.speed_05 -> {
                speed = 0.5f
            }
            R.id.speed_06 -> {
                speed = 0.6f
            }
            R.id.speed_07 -> {
                speed = 0.7f
            }
            R.id.speed_08 -> {
                speed = 0.8f
            }
            R.id.speed_09 -> {
                speed = 0.9f
            }
            R.id.speed_100 -> {
                speed = 1.0f
            }
            R.id.speed_125 -> {
                speed = 1.25f
            }
            R.id.speed_150 -> {
                speed = 1.5f
            }
            R.id.speed_175 -> {
                speed = 1.75f
            }
            R.id.speed_200 -> {
                speed = 2.0f
            }
        }
        PlayerUtil.playSpeed = speed
        if (speed == 1.0f) {
            binding.playSpeed.text = getString(R.string.title_speed)
        } else {
            binding.playSpeed.text = "$speed"
        }
    }

    private fun initSpeedRadioButton() {
        val speed = PlayerUtil.playSpeed
        when (speed) {
            0.3f -> binding.speed03.isChecked = true
            0.4f -> binding.speed04.isChecked = true
            0.5f -> binding.speed05.isChecked = true
            0.6f -> binding.speed06.isChecked = true
            0.7f -> binding.speed07.isChecked = true
            0.8f -> binding.speed08.isChecked = true
            0.9f -> binding.speed09.isChecked = true
            1.0f -> binding.speed100.isChecked = true
            1.25f -> binding.speed125.isChecked = true
            1.5f -> binding.speed150.isChecked = true
            1.75f -> binding.speed175.isChecked = true
            2.0f -> binding.speed200.isChecked = true
        }
        if (speed == 1.0f) {
            binding.playSpeed.text = getString(R.string.title_speed)
        } else {
            binding.playSpeed.text = "$speed"
        }
    }
}