package com.messi.languagehelper

import android.graphics.Bitmap
import android.net.http.SslError
import android.os.Bundle
import android.text.TextUtils
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.webkit.SslErrorHandler
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import cn.leancloud.LCObject
import cn.leancloud.LCQuery
import com.facebook.drawee.view.SimpleDraweeView
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.StyledPlayerView
import com.messi.languagehelper.adModel.FullScreenVideoADModel
import com.messi.languagehelper.adapter.RcXVideoDetailListAdapter
import com.messi.languagehelper.adapter.RcXVideoDetailListItemViewHolder
import com.messi.languagehelper.bean.PVideoResult
import com.messi.languagehelper.bean.ToutiaoVideoBean
import com.messi.languagehelper.bean.ToutiaoWebRootBean
import com.messi.languagehelper.databinding.XvideoDetailActivityBinding
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.http.UICallback
import com.messi.languagehelper.httpservice.RetrofitApiService
import com.messi.languagehelper.httpservice.RetrofitBuilder
import com.messi.languagehelper.util.*
import com.messi.languagehelper.util.NetworkUtil.getNetworkType
import com.messi.languagehelper.util.PlayerUtil.pause
import com.messi.languagehelper.util.ToastUtil.diaplayMesShort
import com.mzxbkj.baselibrary.util.BaseSetings
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.*
import java.util.zip.CRC32

class XVideoDetailActivity : BaseActivity(), Player.Listener {

    private var player: ExoPlayer? = null
    private var mAVObjects: MutableList<LCObject> = ArrayList()
    private var position = 0
    private var cover_img: SimpleDraweeView? = null
    private var videoAdapter: RcXVideoDetailListAdapter? = null
    private var player_view: StyledPlayerView? = null
    private var mWebView: WebView? = null
    private var snapHelper: PagerSnapHelper? = null
    private var layoutManager: LinearLayoutManager? = null
    private var interceptUrls: Array<String>? = null
    private var isIntercept = false
    private var isWVPSuccess = false
    private val isAD = false
    private var mFSVADModel: FullScreenVideoADModel? = null
    private var loading = false
    private var hasMore = true
    private var category: String? = null
    private var keyword: String? = null
    private var pos = -1
    private var viewHolder: RcXVideoDetailListItemViewHolder? = null
    private var binding: XvideoDetailActivityBinding? = null

    private val mListener: RecyclerView.OnScrollListener =
        object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                val visible = layoutManager!!.childCount
                val total = layoutManager.itemCount
                val firstVisibleItem = layoutManager.findFirstCompletelyVisibleItemPosition()
                if (!loading && hasMore) {
                    if (visible + firstVisibleItem >= total) {
                        LogUtil.DefalutLog("RecyclerView:onScrolled")
                        doRequestAsyncTask()
                    }
                }
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                when (newState) {
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        if (player_view != null) {
                            player_view!!.hideController()
                        }
                        val view = snapHelper!!.findSnapView(layoutManager)
                        viewHolder =
                            recyclerView.getChildViewHolder(view!!) as RcXVideoDetailListItemViewHolder
                        if (viewHolder != null) {
                            mProgressbar = viewHolder!!.progress_bar
                            cover_img = viewHolder!!.cover_img
                            if (viewHolder!!.mAVObject != null) {
                                val current = mAVObjects!!.indexOf(viewHolder!!.mAVObject)
                                LogUtil.DefalutLog("pos:$pos-current:$current")
                                if (pos != current) {
                                    pos = current
                                    player!!.stop()
                                    setData(viewHolder!!)
                                }
                            }
                        }
                    }
                    RecyclerView.SCROLL_STATE_DRAGGING -> {}
                    RecyclerView.SCROLL_STATE_SETTLING -> {}
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        transparentStatusbar()
        binding = XvideoDetailActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding!!.root)
        setStatusbarColor(R.color.black)
        initData()
        initWebView()
        addListener()
    }

    private fun initData() {
        try {
            category = intent.getStringExtra(KeyUtil.Category)
            keyword = intent.getStringExtra(KeyUtil.KeyWord)
            pause(this)
            val list = Setings.dataMap[KeyUtil.DataMapKey] as List<LCObject>?
            if (list != null) {
                mAVObjects.addAll(list)
                Setings.dataMap.clear()
                position = intent.getIntExtra(KeyUtil.PositionKey, 0)
            } else {
                val serializedStr = intent.getStringExtra(KeyUtil.AVObjectKey)
                if (TextUtils.isEmpty(serializedStr)) {
                    finish()
                }
                val mAVObject = LCObject.parseLCObject(serializedStr)
                if (mAVObject == null) {
                    finish()
                }
                mAVObjects.add(mAVObject)
                doRequestAsyncTask()
            }
            onClick()
            player_view = LayoutInflater.from(this)
                .inflate(R.layout.xvideo_detail_list_playerview, null) as StyledPlayerView
            player = ExoPlayer.Builder(this).build()
            player!!.addListener(this)
            player!!.repeatMode = Player.REPEAT_MODE_ALL
            player_view!!.player = player
            player_view!!.controllerAutoShow = false
            snapHelper = PagerSnapHelper()
            snapHelper!!.attachToRecyclerView(binding!!.listview)
            videoAdapter = RcXVideoDetailListAdapter()
            layoutManager =
                LinearLayoutManager(this@XVideoDetailActivity, LinearLayoutManager.VERTICAL, false)
            videoAdapter!!.setItems(mAVObjects)
            binding!!.listview.layoutManager = layoutManager
            binding!!.listview.adapter = videoAdapter
            binding!!.listview.scrollToPosition(position)
            mFSVADModel = FullScreenVideoADModel(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun addListener() {
        binding!!.listview.addOnScrollListener(mListener)
        lifecycleScope.launch {
            delay(200)
            mListener.onScrollStateChanged(
                binding!!.listview,
                RecyclerView.SCROLL_STATE_IDLE
            )
        }
    }

    private fun setData(viewHolder: RcXVideoDetailListItemViewHolder) {
        ViewUtil.removeParentView(player_view)
        if (viewHolder.mAVObject[KeyUtil.ADKey] != null || viewHolder.mAVObject[KeyUtil.TXADView] != null || viewHolder.mAVObject[KeyUtil.VideoAD] != null) {
            mFSVADModel!!.setAd_layout(
                viewHolder.mAVObject,
                viewHolder.player_view_layout,
                viewHolder.title,
                viewHolder.btn_detail
            )
            mFSVADModel!!.showAd()
            hideCoverImg()
            hideProgressbar()
            return
        }
        LogUtil.DefalutLog("type:" + viewHolder.type)
        viewHolder.player_view_layout.addView(player_view)
        if (!TextUtils.isEmpty(viewHolder.mAVObject.getString(KeyUtil.VideoParseUrl))) {
            exoplaer(viewHolder.mAVObject.getString(KeyUtil.VideoParseUrl))
            return
        }
        if (!TextUtils.isEmpty(viewHolder.type)) {
            val vid = viewHolder.mAVObject.getString(AVOUtil.XVideo.vid)
            if (!TextUtils.isEmpty(vid)) {
                parseToutiaoVid(vid, viewHolder.Url)
            } else if ("url_api" == viewHolder.type) {
                parseVideoUrl(viewHolder.Url)
            } else if ("url_media" == viewHolder.type) {
                exoplaer(viewHolder.media_url)
            } else if ("url_intercept" == viewHolder.type) {
                isIntercept = true
                if (viewHolder.Url != null && viewHolder.Url.contains("bilibili")) {
                    mWebView!!.settings.userAgentString = Setings.Hearder
                } else {
                    mWebView!!.settings.userAgentString = ""
                }
                mWebView!!.loadUrl(viewHolder.Url)
            } else {
                parseVideoUrl(viewHolder.Url)
            }
        } else {
            parseVideoUrl(viewHolder.Url)
        }
    }

    private fun initWebView() {
        val sp = Setings.getSharedPreferences(this)
        val intercepts = sp.getString(KeyUtil.InterceptUrls, "")
        LogUtil.DefalutLog("InterceptUrls:$intercepts")
        if (!TextUtils.isEmpty(intercepts)) {
            interceptUrls = if (intercepts!!.contains(",")) {
                intercepts.split(",".toRegex()).toTypedArray()
            } else {
                arrayOf(intercepts)
            }
        }
        mWebView = WebView(this)
        mWebView!!.requestFocus()
        mWebView!!.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        mWebView!!.settings.javaScriptEnabled = true
        mWebView!!.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap) {
                super.onPageStarted(view, url, favicon)
                LogUtil.DefalutLog("WebViewClient:onPageStarted")
            }

            override fun onReceivedError(
                view: WebView,
                errorCode: Int,
                description: String,
                failingUrl: String
            ) {
                super.onReceivedError(view, errorCode, description, failingUrl)
            }

            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
                LogUtil.DefalutLog("WebViewClient:onPageFinished")
                hideProgressbar()
                hideCoverImg()
                if (!isWVPSuccess && viewHolder != null) {
                    parseVideoUrl(viewHolder!!.Url)
                }
            }

            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                LogUtil.DefalutLog("shouldOverrideUrlLoading:$url")
                return if (url.contains("bilibili:")) {
                    true
                } else super.shouldOverrideUrlLoading(view, url)
            }

            override fun shouldInterceptRequest(view: WebView, url: String): WebResourceResponse? {
                LogUtil.DefalutLog("shouldInterceptRequest:$url")
                interceptUrl(url)
                return super.shouldInterceptRequest(view, url)
            }

            override fun onReceivedSslError(
                view: WebView,
                handler: SslErrorHandler,
                error: SslError
            ) {
                DialogUtil.OnReceivedSslError(this@XVideoDetailActivity, handler)
            }
        }
    }

    private fun interceptUrl(url: String) {
        var isPlay = false
        if (interceptUrls != null) {
            for (str in interceptUrls!!) {
                if (url.contains(str)) {
                    isWVPSuccess = true
                    isPlay = true
                    break
                }
            }
            if (isPlay) {
                runOnUiThread {
                    if (viewHolder != null && viewHolder!!.mAVObject != null && "头条小视频" == viewHolder!!.mAVObject.getString(
                            AVOUtil.XVideo.source_name
                        )
                    ) {
                        parseToutiaoWebApi(url)
                    } else {
                        exoplaer(url)
                    }
                }
            }
        }
    }

    private fun parseToutiaoWebApi(url: String) {
        LogUtil.DefalutLog("parseToutiaoWebApi:$url")
        LanguagehelperHttpClient.get(url, object : UICallback(this) {
            override fun onFailured() {
                LogUtil.DefalutLog("parseToutiaoWebApi-onFailured")
                if (viewHolder != null) {
                    parseVideoUrl(viewHolder!!.Url)
                }
            }

            override fun onFinished() {}
            override fun onResponsed(responseString: String) {
                var videoUrl = ""
                try {
                    if (!TextUtils.isEmpty(responseString)) {
                        val start = responseString.indexOf("{")
                        val end = responseString.lastIndexOf("}")
                        if (start > 0 && end > 0) {
                            val jstr = responseString.substring(start, end + 1)
                            LogUtil.DefalutLog("jstr:$jstr")
                            if (JsonParser.isJson(jstr)) {
                                val moshi = Moshi.Builder().build()
                                val jsonAdapter = moshi.adapter(ToutiaoWebRootBean::class.java)
                                val result = jsonAdapter.fromJson(jstr)
                                if (result != null && result.data != null && result.data.video_list != null && result.data.video_list.video_1 != null) {
                                    videoUrl = result.data.video_list.video_1.main_url
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    videoUrl = ""
                    e.printStackTrace()
                } finally {
                    if (!TextUtils.isEmpty(videoUrl)) {
                        exoplaer(videoUrl)
                    } else {
                        onFailured()
                    }
                }
            }
        })
    }

    private fun parseVideoUrl(url: String) {
        LogUtil.DefalutLog("parseVideoUrl:$url")
        if (TextUtils.isEmpty(url)) {
            return
        }
        showProgressbar()
        var vid: String? = ""
        if (viewHolder != null && viewHolder!!.mAVObject != null &&
            !TextUtils.isEmpty(viewHolder!!.mAVObject.getString(AVOUtil.XVideo.vid))
        ) {
            vid = viewHolder!!.mAVObject.getString(AVOUtil.XVideo.vid)
        }
        val timestamp = System.currentTimeMillis().toString()
        val platform = SystemUtil.platform
        val network = getNetworkType(this)
        val sign = SignUtil.getMd5Sign(BaseSetings.PVK, timestamp, url, platform, network)
        val service = RetrofitApiService.getRetrofitApiService(
            RetrofitBuilder.XBKJWEB_BASE_URL,
            RetrofitApiService::class.java
        )
        val call = service.getPVideoApi(url, network, platform, sign, timestamp, 0, vid)
        call.enqueue(object : Callback<PVideoResult?> {
            override fun onResponse(call: Call<PVideoResult?>, response: Response<PVideoResult?>) {
                LogUtil.DefalutLog("onResponse:$response---call:$call")
                if (response.isSuccessful) {
                    val mResult = response.body()
                    if (mResult != null && !TextUtils.isEmpty(mResult.url)) {
                        if (viewHolder!!.mAVObject != null) {
                            viewHolder!!.mAVObject.put(KeyUtil.VideoParseUrl, mResult.url)
                        }
                        exoplaer(mResult.url)
                    }
                } else {
                    hideProgressbar()
                    diaplayMesShort(this@XVideoDetailActivity, "视频不见了，换一个吧")
                }
            }

            override fun onFailure(call: Call<PVideoResult?>, t: Throwable) {
                LogUtil.DefalutLog("onFailure:" + t.message + "---call:" + call.request().url)
                hideProgressbar()
            }
        }
        )
    }

    private fun exoplaer(media_url: String?) {
        hideProgressbar()
        if (player == null) {
            player = ExoPlayer.Builder(this).build()
        }
        val mediaItem = MediaItem.fromUri(
            media_url!!
        )
        player!!.setMediaItem(mediaItem)
        player!!.prepare()
        player!!.play()
        LogUtil.DefalutLog("ACTION_setPlayWhenReady")
    }

    override fun onPlaybackStateChanged(playbackState: Int) {
        LogUtil.DefalutLog("onPlayerStateChanged:$playbackState")
        if (playbackState == Player.STATE_READY) {
            hideCoverImg()
        }
    }

    private fun doRequestAsyncTask() {
        LogUtil.DefalutLog("should load data")
        lifecycleScope.launch {
            val result = requestAsyncTask()
            doRequestAsyncTaskDone(result)
        }
    }

    private suspend fun requestAsyncTask(): List<LCObject>? = withContext(Dispatchers.IO) {
        var results: List<LCObject>? = null
        try{
            var publishTime: Date? = null
            if (mAVObjects.isNotEmpty()) {
                publishTime = mAVObjects[mAVObjects.size - 1].getDate(AVOUtil.XVideo.publish_time)
            }
            loading = true
            var query = LCQuery<LCObject>(AVOUtil.XVideo.XVideo)
            LogUtil.DefalutLog("category:$category")
            if (!TextUtils.isEmpty(category)) {
                query.whereEqualTo(AVOUtil.XVideo.category, category)
            }
            if (!TextUtils.isEmpty(keyword)) {
                val priorityQuery = LCQuery<LCObject>(AVOUtil.XVideo.XVideo)
                priorityQuery.whereContains(AVOUtil.XVideo.title, keyword)
                val statusQuery = LCQuery<LCObject>(AVOUtil.XVideo.XVideo)
                statusQuery.whereContains(AVOUtil.XVideo.tag, keyword)
                query = LCQuery.or(listOf(priorityQuery, statusQuery))
            }
            publishTime?.let {
                query.whereLessThan(AVOUtil.XVideo.publish_time, publishTime)
            }
            query.orderByDescending(AVOUtil.XVideo.publish_time)
            query.limit(Setings.page_size)
            results = query.find()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        results
    }

    private fun doRequestAsyncTaskDone(list: List<LCObject>?) {
        loading = false
        if (list != null) {
            hasMore = if (list.isEmpty()) {
                false
            } else {
                mAVObjects.addAll(list)
                loadAD()
                list.size >= Setings.page_size
            }
        } else {
            diaplayMesShort(this@XVideoDetailActivity, "加载失败，下拉可刷新")
        }
    }

    private fun loadAD() {
        if (mFSVADModel != null) {
            LogUtil.DefalutLog("should load ad")
            mFSVADModel!!.setAVObjects(mAVObjects)
            mFSVADModel!!.setVideoAdapter(videoAdapter)
            mFSVADModel!!.justLoadData()
        }
    }

    override fun onPause() {
        super.onPause()
        if (player != null) {
            player!!.playWhenReady = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    fun releasePlayer() {
        if (player != null) {
            player!!.playWhenReady = false
            player!!.release()
            player = null
        }
    }

    fun onClick() {
        binding!!.backBtn.setOnClickListener { onBackPressed() }
    }

    private fun hideCoverImg() {
        if (cover_img != null) {
            cover_img!!.visibility = View.GONE
        }
    }

    private fun parseToutiaoVid(vid: String, backUrl: String) {
        LogUtil.DefalutLog("vid:$vid")
        var videoUrl = ""
        try {
//            String pattern = "\"vid\":\"(\\w*)\"";
            val randint = StringUtils.getRandomString(16)
            val videoid = "/video/urls/v/1/toutiao/mp4/$vid?r=$randint"
            val crc32 = CRC32()
            crc32.update(videoid.toByteArray())
            val checksum = crc32.value
            videoUrl = "http://i.snssdk.com$videoid&s=$checksum"
            LogUtil.DefalutLog("videoid:$videoid-videoUrl:$videoUrl")
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            parseToutiaoApi(videoUrl, backUrl)
        }
    }

    private fun parseToutiaoApi(url: String, backUrl: String) {
        LogUtil.DefalutLog("parseToutiaoApi")
        LanguagehelperHttpClient.get(url, object : UICallback(this) {
            override fun onFailured() {
                parseVideoUrl(backUrl)
            }

            override fun onFinished() {}
            override fun onResponsed(responseStr: String) {
//                LogUtil.DefalutLog("parseToutiaoApi-responseStr:"+responseStr);
                var videoUrl: String? = ""
                try {
                    val moshi = Moshi.Builder().build()
                    val jsonAdapter = moshi.adapter(ToutiaoVideoBean::class.java)
                    val toutiaoVideo = jsonAdapter.fromJson(responseStr)
                    if (toutiaoVideo?.code == 0) {
                        videoUrl = String(
                            Base64.decode(
                                toutiaoVideo.data.video_list.video_1.main_url.toByteArray(),
                                Base64.DEFAULT
                            )
                        )
                    }
                } catch (e: Exception) {
                    onFailured()
                    e.printStackTrace()
                } finally {
                    if (!TextUtils.isEmpty(videoUrl)) {
                        exoplaer(videoUrl)
                    } else {
                        onFailured()
                    }
                }
            }
        })
    }
}