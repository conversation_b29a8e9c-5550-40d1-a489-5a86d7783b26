package com.messi.languagehelper

import android.app.Dialog
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Bitmap
import android.net.http.SslError
import android.os.Bundle
import android.os.PersistableBundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.SslErrorHandler
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.exoplayer2.C
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.PlaybackException
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.util.RepeatModeUtil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.ReadingFragmentYYS.Companion.newInstance
import com.messi.languagehelper.bean.BoutiquesBean
import com.messi.languagehelper.bean.PVideoResult
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.BoxHelper.insert
import com.messi.languagehelper.box.BoxHelper.isCollected
import com.messi.languagehelper.box.BoxHelper.remove
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.databinding.ReadDetailToutiaoActivityBinding
import com.messi.languagehelper.impl.FragmentProgressbarListener
import com.messi.languagehelper.util.*
import com.messi.languagehelper.util.MyPlayer.getMediaSource
import com.messi.languagehelper.util.NetworkUtil.isWifiConnected
import com.messi.languagehelper.viewmodels.VideoListViewModel
import com.messi.languagehelper.viewmodels.VideoPageModel
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ReadVideoActivity : BaseActivity(), FragmentProgressbarListener, Player.Listener {

    private val STATE_RESUME_WINDOW = "resumeWindow"
    private val STATE_RESUME_POSITION = "resumePosition"
    private val STATE_PLAYER_FULLSCREEN = "playerFullscreen"
    private var Url: String? = null
    private lateinit var mAVObject: NDetail
    private lateinit var player: ExoPlayer
    private lateinit var mFullScreenButton: FrameLayout
    private lateinit var back_btn: LinearLayout
    private lateinit var mFullScreenIcon: ImageView
    private var mFullScreenDialog: Dialog? = null
    private var mExoPlayerFullscreen = false
    private var mResumeWindow = 0
    private var mResumePosition: Long = 0
    private var interceptUrls: Array<String>? = null
    private var isIntercept = false
    private lateinit var binding: ReadDetailToutiaoActivityBinding
    private lateinit var playListSheet: BottomSheetBehavior<View>
    private val viewModel: VideoPageModel by viewModels()
    private val dataListModel: VideoListViewModel by viewModels()
    private var boutique_code: String? = ""
    private var category: String? = ""
    private var startPosition = 0L
    private var isCollected = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.DefalutLog("ReadVideoActivity---onCreate")
        binding = ReadDetailToutiaoActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        showBuffering()
        initViews()
        initFullscreenDialog()
        initFullscreenButton()
        init(savedInstanceState)
    }

    private fun init(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            mResumeWindow = savedInstanceState.getInt(STATE_RESUME_WINDOW)
            mResumePosition = savedInstanceState.getLong(STATE_RESUME_POSITION)
            mExoPlayerFullscreen = savedInstanceState.getBoolean(STATE_PLAYER_FULLSCREEN)
        }
        startPosition = intent.getLongExtra(KeyUtil.StartPosition, 0L)
        val item = intent.getParcelableExtra<NDetail>(KeyUtil.ParcelableData)
        if (item != null) {
            dataListModel.position = item.order
            setCurrentItem(item)
        } else {
            val mBoutiquesBean = intent.getParcelableExtra<BoutiquesBean>(KeyUtil.ObjectKey)
            setBoutiquesBean(mBoutiquesBean)
        }
        initCollectState()
        initSeriesAndRelatedList()
    }

    private fun setCurrentItem(item: NDetail) {
        mAVObject = item
        boutique_code = item.boutique_code
        category = item.category
        Url = mAVObject.source_url
        viewModel.currentItem = mAVObject
        binding.titleTv.text = viewModel.getTitle()
        LogUtil.DefalutLog("Url:$Url")
        setData()
        viewModel.updateState()
    }

    private fun setBoutiquesBean(mBoutiquesBean: BoutiquesBean?) {
        dataListModel.isFirstPlay = true
        if (mBoutiquesBean != null) {
            BoxHelper.saveOrGetStatus(mBoutiquesBean)
            LogUtil.DefalutLog("order:${mBoutiquesBean.lastPlayOid}")
            viewModel.mBoutiquesBean = mBoutiquesBean
            Url = mBoutiquesBean.source_url
            dataListModel.position = mBoutiquesBean.lastPlayOid
            dataListModel.total = mBoutiquesBean.total
            boutique_code = mBoutiquesBean.code
            category = mBoutiquesBean.category
            binding.titleTv.text = viewModel.getTitle()
            mAVObject = NDetail()
            mAVObject.title = mBoutiquesBean.title
            mAVObject.source_url = mBoutiquesBean.source_url
            mAVObject.order = mBoutiquesBean.lastPlayOid
            mAVObject.source_url = mBoutiquesBean.source_url
            mAVObject.source_name = mBoutiquesBean.source_name
            mAVObject.boutique_code = mBoutiquesBean.code
            viewModel.parseVideoUrl(mAVObject)
        }
    }

    private fun initViews() {
        PlayerUtil.pause(this)
        player = ExoPlayer.Builder(this).build()
        val sp = Setings.getSharedPreferences(this)
        val intercepts = sp.getString(KeyUtil.InterceptUrls, "")
        LogUtil.DefalutLog("InterceptUrls:$intercepts")
        if (!TextUtils.isEmpty(intercepts)) {
            interceptUrls = if (intercepts!!.contains(",")) {
                intercepts.split(",".toRegex()).toTypedArray()
            } else {
                arrayOf(intercepts)
            }
        }
        setStatusbarColor(R.color.black)
        binding.bottomSheetPlaylist.visibility = View.VISIBLE
        playListSheet = BottomSheetBehavior.from(binding.bottomSheetPlaylist)
        playListSheet.state = BottomSheetBehavior.STATE_HIDDEN
        binding.collectLayout.setOnClickListener { onCollectBtnClick() }
        binding.playlistSheetCloseBtn.setOnClickListener {
            playListSheet.state = BottomSheetBehavior.STATE_HIDDEN
        }
        binding.playListBtn.setOnClickListener {
            playListSheet.state = BottomSheetBehavior.STATE_EXPANDED
        }
        binding.playerView.controllerAutoShow = false
        initLiveData()
    }

    private fun setData() {
        LogUtil.DefalutLog("content_type:${mAVObject.content_type}")
        when (mAVObject.content_type) {
            "url_media" -> {
                exoplaer(mAVObject.media_url)
            }
            "url_intercept" -> {
                interceptUrl()
            }
            else -> {
                if (TextUtils.isEmpty(mAVObject.vid)) {
                    mAVObject.vid = ""
                }
                viewModel.parseWebSource(Url!!, mAVObject.vid)
            }
        }
    }

    private fun initLiveData() {
        viewModel.ldPVideoResult.observe(this) {
            if (it.code == 1 && it.data != null) {
                exoplaer(it.data!!.url!!, it.data)
            } else {
                ToastUtil.diaplayMesLong(this, it.errStr)
            }
        }
        viewModel.playNext.observe(this) {
            setCurrentItem(it)
        }
    }

    private fun initCollectState() {
        if (!TextUtils.isEmpty(boutique_code)) {
            if (isCollected(boutique_code)) {
                isCollected = true
            }
        }
        if (::mAVObject.isInitialized && !TextUtils.isEmpty(mAVObject.object_id) && !isCollected) {
            if (isCollected(mAVObject.object_id)) {
                isCollected = true
            }
        }
        setCollectStatus()
    }

    private fun interceptUrl() {
        setWebView()
        showBuffering()
        isIntercept = true
        binding.refreshableWebview.loadUrl(Url!!)
    }

    private fun showWebView() {
        hideBuffering()
        isIntercept = false
        binding.webviewLayout.visibility = View.VISIBLE
        binding.videoLy.visibility = View.GONE
        binding.refreshableWebview.loadUrl(Url!!)
    }

    private fun setWebView() {
        binding.refreshableWebview.requestFocus()
        binding.refreshableWebview.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        binding.refreshableWebview.settings.javaScriptEnabled = true
        binding.refreshableWebview.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                LogUtil.DefalutLog("WebViewClient:onPageStarted")
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                LogUtil.DefalutLog("WebViewClient:onPageFinished")
            }

            override fun shouldInterceptRequest(
                view: WebView?,
                url: String?
            ): WebResourceResponse? {
                LogUtil.DefalutLog("shouldInterceptRequest1:$url")
                if (url != null) {
                    interceptUrl(url)
                }
                return super.shouldInterceptRequest(view, url)
            }

            override fun onReceivedSslError(
                view: WebView?,
                handler: SslErrorHandler?,
                error: SslError?
            ) {
                DialogUtil.OnReceivedSslError(this@ReadVideoActivity, handler)
            }
        }
    }

    private fun interceptUrl(url: String) {
        if (isIntercept) {
            if (interceptUrls != null) {
                for (str in interceptUrls!!) {
                    if (url.contains(str)) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            LogUtil.DefalutLog("runOnUiThread")
                            if ("今日头条" == mAVObject.source_name || "西瓜视频" == mAVObject.source_name) {
                                viewModel.parseToutiaoWebApi(url, Url, mAVObject.vid)
                            } else {
                                exoplaer(url)
                            }
                        }
                        break
                    }
                }
            }
        }
    }

    private fun exoplaer(media_url: String, mResult: PVideoResult? = null, mp3Url: String? = null) {
        LogUtil.DefalutLog("exoplaer---media_url:$media_url")
        LogUtil.DefalutLog("exoplaer---media_url:$Url")
        LogUtil.DefalutLog("exoplaer---media_url:${mAVObject.backup1}")
        lifecycleScope.launch {
            hideBuffering()
            binding.playerView.hideController()
            mAVObject.media_url = media_url
            if (mResult != null && !TextUtils.isEmpty(mResult.mp3Url)) {
                mAVObject.backup1 = mResult.mp3Url
            }
            if (mp3Url != null) {
                mAVObject.backup1 = mp3Url
            }
            binding.videoLy.visibility = View.VISIBLE
            binding.refreshableWebview.visibility = View.GONE

            binding.playerView.player = player
            binding.playerView.setRepeatToggleModes(RepeatModeUtil.REPEAT_TOGGLE_MODE_ONE)
            val haveResumePosition = mResumeWindow != C.INDEX_UNSET
            if (haveResumePosition) {
                binding.playerView.player!!.seekTo(mResumeWindow, mResumePosition)
            }
            val mediaSource =
                getMediaSource(media_url, mAVObject.backup1, Url!!, this@ReadVideoActivity)
            player.playWhenReady = true
            player.addListener(this@ReadVideoActivity)
            player.setMediaSource(mediaSource, startPosition)
            player.prepare()
            LogUtil.DefalutLog("ACTION_setPlayWhenReady")
        }
    }

    override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
        LogUtil.DefalutLog("onPlayerStateChanged:$playbackState-:playWhenReady:$playWhenReady")
        if (playWhenReady && playbackState == Player.STATE_ENDED) {
            playNext()
        }
    }

    private fun playNext() {
        lifecycleScope.launch {
            delay(1000)
            dataListModel.playNext(mAVObject)
        }
    }

    override fun onPlayerError(error: PlaybackException) {}

    override fun onSaveInstanceState(outState: Bundle, outPersistentState: PersistableBundle) {
        outState.putInt(STATE_RESUME_WINDOW, mResumeWindow)
        outState.putLong(STATE_RESUME_POSITION, mResumePosition)
        outState.putBoolean(STATE_PLAYER_FULLSCREEN, mExoPlayerFullscreen)
        super.onSaveInstanceState(outState, outPersistentState)
    }

    private fun initFullscreenDialog() {
        mFullScreenDialog =
            object : Dialog(this, android.R.style.Theme_Black_NoTitleBar_Fullscreen) {
                override fun onBackPressed() {
                    if (mExoPlayerFullscreen) closeFullscreenDialog()
                    super.onBackPressed()
                }
            }
    }

    private fun openFullscreenDialog() {
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        (binding.playerView.parent as ViewGroup).removeView(binding.playerView)
        mFullScreenDialog!!.addContentView(
            binding.playerView,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
        mFullScreenIcon.setImageDrawable(getDrawable(R.drawable.ic_fullscreen_exit_white))
        mExoPlayerFullscreen = true
        mFullScreenDialog!!.show()
    }

    private fun closeFullscreenDialog() {
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        (binding.playerView.parent as ViewGroup).removeView(binding.playerView)
        binding.videoLy.addView(binding.playerView)
        mExoPlayerFullscreen = false
        mFullScreenDialog!!.dismiss()
        mFullScreenIcon.setImageDrawable(getDrawable(R.drawable.ic_fullscreen_white))
    }

    private fun initFullscreenButton() {
        back_btn = binding.playerView.findViewById(R.id.back_btn)
        mFullScreenIcon = binding.playerView.findViewById(R.id.exo_fullscreen_icon)
        mFullScreenButton = binding.playerView.findViewById(R.id.exo_fullscreen_button)
        val exoListen = binding.playerView.findViewById(R.id.exo_listen) as FrameLayout
        back_btn.setOnClickListener { onBack_btn() }
        exoListen.setOnClickListener { onBackGroundClicked() }
        mFullScreenButton.setOnClickListener {
            if (!mExoPlayerFullscreen) {
                openFullscreenDialog()
            } else {
                closeFullscreenDialog()
            }
        }
    }

    override fun onBackPressed() {
        if (::playListSheet.isInitialized) {
            if (playListSheet.state == BottomSheetBehavior.STATE_EXPANDED) {
                playListSheet.state = BottomSheetBehavior.STATE_HIDDEN
            } else {
                super.onBackPressed()
            }
        } else {
            super.onBackPressed()
        }
    }

    private fun showBuffering() {
        binding.progressbar.visibility = View.VISIBLE
    }

    private fun hideBuffering() {
        binding.progressbar.visibility = View.GONE
    }

    private fun initSeriesAndRelatedList() {
        if (!TextUtils.isEmpty(boutique_code)) {
            addVideoNewsList()
        } else {
            addRelatedList()
        }
    }

    private fun addVideoNewsList() {
        val videoListFragment: Fragment = VideoListFragment.Builder()
            .boutique_code(boutique_code)
            .build()
        supportFragmentManager
            .beginTransaction()
            .add(R.id.pager, videoListFragment)
            .commit()
    }

    private fun addRelatedList() {
        val fragment = newInstance()
        supportFragmentManager
            .beginTransaction()
            .add(R.id.pager, fragment)
            .commit()
    }

    override fun onDestroy() {
        super.onDestroy()
        ViewUtil.destroyWebView(binding.refreshableWebview)
        releasePlayer()
    }

    private fun onBackGroundClicked() {
        if (::mAVObject.isInitialized && !TextUtils.isEmpty(mAVObject.media_url)) {
            var currentPosition: Long = 0
            if (::player.isInitialized) {
                currentPosition = player.currentPosition
            }
            if (dataListModel.avObjects.isNotEmpty()) {
                var pos = 0
                for ((index, item) in dataListModel.avObjects.withIndex()) {
                    if (item.source_url == mAVObject.source_url) {
                        item.media_url = mAVObject.media_url
                        item.backup1 = mAVObject.backup1
                        pos = index
                        break
                    }
                }
                PlayerUtil.initVideoBgPlayList(dataListModel.avObjects, pos, currentPosition, "")
            } else {
                PlayerUtil.initAndPlay(mAVObject, true, currentPosition)
            }
            onBackPressed()
        }
    }

    override fun onPause() {
        super.onPause()
        if (::player.isInitialized) {
            player.playWhenReady = false
        }
    }

    private fun onBack_btn() {
        if (!mExoPlayerFullscreen) {
            onBackPressed()
        } else {
            closeFullscreenDialog()
        }
    }

    private fun releasePlayer() {
        if (::player.isInitialized) {
            player.playWhenReady = false
            player.release()
        }
        if (mFullScreenDialog != null) {
            mFullScreenDialog!!.dismiss()
        }
    }

    private fun onCollectBtnClick() {
        isCollected = !isCollected
        setCollectStatus()
        updateData()
    }

    private fun setCollectStatus() {
        if (isCollected) {
            binding.collectLayout.setImageResource(R.drawable.ic_collected_white)
        } else {
            binding.collectLayout.setImageResource(R.drawable.ic_uncollected_white)
        }
    }

    private fun updateData() {
        lifecycleScope.launch(Dispatchers.IO) {
            if (isCollected) {
                if (!TextUtils.isEmpty(boutique_code)) {
                    var mBoutiquesBean: BoutiquesBean? = null
                    mBoutiquesBean = viewModel.mBoutiquesBean
                    if (mBoutiquesBean == null) {
                        mBoutiquesBean = CloudAndDatabaseHelper.getBoutiqueByCode(boutique_code!!)
                    }
                    if (mBoutiquesBean != null) {
                        val cdata = CollectedData()
                        cdata.objectId = mBoutiquesBean.code
                        cdata.name = mBoutiquesBean.title
                        cdata.type = AVOUtil.Boutiques.Boutiques
                        val moshi = Moshi.Builder().build()
                        val jsonAdapter = moshi.adapter(BoutiquesBean::class.java)
                        cdata.json = jsonAdapter.toJson(mBoutiquesBean)
                        insert(cdata)
                    }
                } else if (::mAVObject.isInitialized && !TextUtils.isEmpty(mAVObject.object_id)) {
                    val cdata = CollectedData()
                    cdata.objectId = mAVObject.object_id
                    cdata.name = mAVObject.title
                    cdata.type = AVOUtil.Reading.Reading
                    val moshi = Moshi.Builder().build()
                    val jsonAdapter = moshi.adapter(NDetail::class.java)
                    cdata.json = jsonAdapter.toJson(mAVObject)
                    insert(cdata)
                }
            } else {
                if (!TextUtils.isEmpty(boutique_code)) {
                    val cdata = CollectedData()
                    cdata.objectId = boutique_code
                    remove(cdata)
                }
                if (::mAVObject.isInitialized && !TextUtils.isEmpty(mAVObject.object_id)) {
                    val cdata = CollectedData()
                    cdata.objectId = mAVObject.object_id
                    remove(cdata)
                }
            }
            LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
        }
    }
}