package com.messi.languagehelper.util

import android.text.TextUtils
import android.util.Base64
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.messi.languagehelper.bean.*
import com.messi.languagehelper.box.Record
import com.messi.languagehelper.box.TranResultZhYue
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.httpservice.RetrofitBuilder
import com.mzxbkj.baselibrary.util.BaseSetings
import com.squareup.moshi.Moshi
import okhttp3.Request
import org.jsoup.Jsoup
import java.net.URLEncoder
import java.security.MessageDigest
import java.util.Locale
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object KTranYueHelper {

    private var OrderTran = "xbkj,bdjs,jscbjs,baidu"
    private const val baidu = "baidu"
    private const val youdaoapi = "youdaoapi"
    private const val bingweb = "bingweb"
    private const val bdjs = "bdjs"
    private const val xbkj = "xbkj"
    private const val jscbjs = "jscbjs"
    private const val hujiangapi = "hujiangapi"
    private const val hujiangweb = "hujiangweb"
    private const val qqfyj = "qqfyj"
    private lateinit var tranOrder: MutableList<String>

    fun initTranOrder(orderStr: String?) {
        LogUtil.DefalutLog("---initTranOrder---")
        try {
            if (!TextUtils.isEmpty(orderStr) && orderStr!!.contains("#")) {
                val keys = orderStr.split("#".toRegex()).toTypedArray()
                if (keys != null && keys.size > 1) {
                    if (!TextUtils.isEmpty(keys[0])) {
                        OrderTran = keys[0]
                    }
                    if (!TextUtils.isEmpty(keys[1])) {
                        DictHelper.OrderDic = keys[1]
                    }
                }
            } else {
                dafultInitValue()
            }
            dafultInitOrder()
        } catch (e: Exception) {
            dafultInitValue()
            dafultInitOrder()
            e.printStackTrace()
        }
    }

    private fun dafultInitValue() {
        OrderTran = "xbkj,bdjs,jscbjs,baidu"
        KDictHelper.OrderDic = "xbkj,bdjs,jscbjs,baidu"
    }

    private fun dafultInitOrder() {
        tranOrder = OrderTran.split(",").toMutableList()
        KDictHelper.dictOrder = KDictHelper.OrderDic.split(",").toMutableList()
    }

    suspend fun doTranslateTask(): TranResultZhYue? {
        if (!::tranOrder.isInitialized) {
            initTranOrder("")
        }
        var mRecordResult: TranResultZhYue? = null
        try {
            for (method in tranOrder) {
                LogUtil.DefalutLog("DoTranslateByMethod---$method")
                mRecordResult = when (method) {
                    bdjs -> {
                        tranFromBdJs()
                    }
                    jscbjs -> {
                        tranFromCbFy()
                    }
                    baidu -> {
                        tranFromXBKJ()
                    }
                    else -> {
                        tranFromXBKJ()
                    }
                }
                if (mRecordResult != null) break
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecordResult
    }

    private suspend fun tranFromXBKJ(): TranResultZhYue? {
        LogUtil.DefalutLog("tranFromXBKJ")
        var mRecord: TranResultZhYue? = null
        try {
            val timestamp = System.currentTimeMillis().toString()
            val platform = SystemUtil.platform
            val network = SystemUtil.network
            val sign = SignUtil.getMd5Sign(
                BaseSetings.TSLVK, timestamp, Setings.q,
                platform, network, Setings.from, Setings.to
            )
            val responseResult = RetrofitBuilder.tService.tranZhYue(
                word = Setings.q,
                fr = Setings.from,
                to = Setings.to,
                network = network,
                platform = platform,
                sign = sign,
                timestamp = timestamp
            )
            val mResult = responseResult.body()
//            LogUtil.DefalutLog(responseResult.message())
            if (mResult != null && mResult.result != null) {
                mRecord = TranResultZhYue(mResult.result.result, Setings.q, Setings.to)
                LogUtil.DefalutLog("Result---tranFromXBKJ")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecord
    }

    private suspend fun tranFromBdJs(): TranResultZhYue? {
        LogUtil.DefalutLog("tranFromBdJs")
        //zh  en  ja  ko  fr  de  es
        var result: TranResultZhYue? = null
        try {
            val from = Setings.from
            val to = Setings.to
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.BdJsTranBaseUrl)
            val sign = BDEncoder().encode(Setings.q)
//            LogUtil.DefalutLog("sign:$sign")
            val mResponseBody = service.tranByBdJsApi(from, to, Setings.q, sign)
            val responseStr = mResponseBody.body()?.string()
            if (!TextUtils.isEmpty(responseStr)) {
                val mBdJsData = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, BdJsData::class.java)
                result = fromBdJsToRecord(mBdJsData)
                LogUtil.DefalutLog("Result---responseStr:${responseStr}")
                LogUtil.DefalutLog("Result---tranFromBdJs:${result.toString()}")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromCbFy(): TranResultZhYue? {
        LogUtil.DefalutLog("tranFromCbFy")
        var result: TranResultZhYue? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbFyApiTranBaseUrl)
            val sign = jscbJsSign(Setings.q)
            LogUtil.DefalutLog("sign:$sign")
            val mResponseBody = service.tranByIciba(
                sign = sign,
                from = Setings.from,
                to = Setings.to,
                q = Setings.q,
            )
            val mCbfyApiRes = mResponseBody.body()
            LogUtil.DefalutLog(mCbfyApiRes.toString())
            if (mResponseBody.isSuccessful && mCbfyApiRes != null) {
                result = fromJscbJsToRecord(mCbfyApiRes)
                LogUtil.DefalutLog("Result---tranFromJscbJs")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromJscbFy(): TranResultZhYue? {
        LogUtil.DefalutLog("tranFromJscbFy")
        var result: TranResultZhYue? = null
        try {
            val from = Setings.from
            val to = Setings.to
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbFyBaseUrl)
            val sign = KSettings.getJscbFySign(Setings.q)
            LogUtil.DefalutLog("sign:$sign")
            val mResponseBody = service.tranByJscbFy(sign, Setings.q, from, to)
            val mJscbFyData = mResponseBody.body()
            if (mJscbFyData != null) {
                if (mJscbFyData.status == 1 && !TextUtils.isEmpty(mJscbFyData.content?.out)) {
                    result = TranResultZhYue(mJscbFyData.content?.out, Setings.q, Setings.to)
                    LogUtil.DefalutLog("Result---tranFromJscbFy")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromJscbApi(): Record? {
        LogUtil.DefalutLog("tranFromJscbApi")
        var result: Record? = null
        try {
            var service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbApiBaseUrl)
            var mResponseBody = service.tranByJscbApi(Setings.q)
            val responseStr = mResponseBody.body()?.string()
//            LogUtil.DefalutLog(responseStr)
            if (!TextUtils.isEmpty(responseStr)) {
                val mBdJsData = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, JscbApiData::class.java)
                result = fromJscbApiToRecord(mBdJsData)
                LogUtil.DefalutLog("Result---tranFromJscbApi")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromQQFYJ(): Record? {
        LogUtil.DefalutLog("tranFromQQFYJ")
        var result: Record? = null
        try {
            val time_stamp = (System.currentTimeMillis() / 1000).toString()
            val nonce_str = StringUtils.getRandomString(16)
            var source = "zh"
            var target = "en"
            if (StringUtils.isEnglish(Setings.q)) {
                source = "en"
                target = "zh"
            }
            var map = sortedMapOf(
                "app_id" to URLEncoder.encode(Setings.QQAPPID, "UTF-8"),
                "nonce_str" to URLEncoder.encode(nonce_str, "UTF-8"),
                "text" to URLEncoder.encode(Setings.q, "UTF-8"),
                "time_stamp" to URLEncoder.encode(time_stamp, "UTF-8"),
                "source" to URLEncoder.encode(source, "UTF-8"),
                "target" to URLEncoder.encode(target, "UTF-8")
            )
            val sign = getSortData(map)
            var service = RetrofitBuilder.getKTranRetrofitService(Setings.QQTranFYJBase)
            var response = service.tranByQQFYJ(
                Setings.QQAPPID,
                time_stamp,
                sign,
                source,
                target,
                Setings.q,
                nonce_str
            )
            val data = response.body()
            if (data != null && data.ret == 0 && data.data != null) {
                result = Record(data.data.target_text, Setings.q)
                LogUtil.DefalutLog("Result---tranFromQQFYJ")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromYDWeb(): Record? {
        LogUtil.DefalutLog("tranFromYDWeb")
        var result: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.YoudaoWeb)
            val response = service.tranByYDWeb(Setings.q)
            val data = response.body()
            if (data != null) {
                val responseString = data.string()
                if (!TextUtils.isEmpty(responseString)) {
                    result = getParseYoudaoWebHtml(responseString)
                    LogUtil.DefalutLog("Result---tranFromYDWeb")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromBingWeb(): Record? {
        LogUtil.DefalutLog("tranFromBingWeb")
        var result: Record? = null
        try {
            var service = RetrofitBuilder.getKTranRetrofitService(Setings.BingyingWebBase)
            var response = service.tranByBingWeb(Setings.q)
            var body = response.body()
            if (body != null) {
                val data = body.string()
                if (!TextUtils.isEmpty(data)) {
                    result = getParseBingyingWebHtml(data)
                    LogUtil.DefalutLog("Result---tranFromBingWeb")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromHjWeb(): Record? {
        LogUtil.DefalutLog("tranFromHjWeb")
        var result: Record? = null
        try {
            val request = Request.Builder()
                .url(Setings.HjiangWeb + StringUtils.replaceAll(Setings.q))
                .header("User-Agent", LanguagehelperHttpClient.Header)
                .header("Cookie", TranslateHelper.HJCookie)
                .build()
            var response = LanguagehelperHttpClient.get(request, null)
            if (response.isSuccessful) {
                val responseString = response.body?.string()
                if (!TextUtils.isEmpty(responseString)) {
                    result = responseString?.let { getParseHjiangWebHtml(it) }
                    LogUtil.DefalutLog("Result---tranFromHjWeb")
                }
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromYDApi(): Record? {
        LogUtil.DefalutLog("tranFromYDApi")
        var mRecord: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.YoudaoApiBase)
            val response = service.tranByYDApi(Setings.q)
            val bean = response.body()
            if (bean != null && bean.errorCode == 0 && bean.translateResult != null) {
                val list = bean.translateResult
                if (list.size > 0) {
                    val item = list[0]
                    if (item != null && item.size > 0) {
                        val result = item[0]
                        if (result != null && !TextUtils.isEmpty(result.tgt)) {
                            mRecord = Record(result.tgt, Setings.q)
                            LogUtil.DefalutLog("Result---tranFromYDApi")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecord
    }

    private fun tranFromHJApi(): Record? {
        LogUtil.DefalutLog("tranFromHJApi")
        var mRecord: Record? = null
        try {
            var response = LanguagehelperHttpClient.postHjApi(null)
            if (response.isSuccessful) {
                var dataString = response.body?.string()
                mRecord = dataString?.let { tran_hj_api(it) }
                LogUtil.DefalutLog("Result---tranFromHJApi")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecord
    }

    @Throws(Exception::class)
    private fun tran_hj_api(mResult: String): Record? {
        var currentDialogBean: Record? = null
        if (!TextUtils.isEmpty(mResult)) {
            if (JsonParser.isJson(mResult)) {
                val moshi = Moshi.Builder().build()
                val jsonAdapter = moshi.adapter(HjTranBean::class.java)
                val mHjTranBean = jsonAdapter.fromJson(mResult)
                if (mHjTranBean != null && mHjTranBean.status == 0 && mHjTranBean.data != null && !TextUtils.isEmpty(
                        mHjTranBean.data.content
                    )
                ) {
                    currentDialogBean = Record(mHjTranBean.data.content, Setings.q)
                }
            }
        }
        LogUtil.DefalutLog("tran_hj_api")
        return currentDialogBean
    }

    @Throws(Exception::class)
    private fun fromBdJsToRecord(mBdJsData: BdJsData): TranResultZhYue? {
        var result: TranResultZhYue? = null
        if (mBdJsData.trans_result?.data?.isNotEmpty() == true) {
            val sb = StringBuilder()
            for (dst in mBdJsData.trans_result.data) {
                sb.append(dst.dst)
                sb.append("\n")
            }
            result = TranResultZhYue(sb.toString().trim(), Setings.q, Setings.to)
            LogUtil.DefalutLog("fromBdJsToRecord：${mBdJsData.trans_result.data[0].dst}")
        }
        LogUtil.DefalutLog("tran_fromBdJs")
        return result
    }

    @Throws(Exception::class)
    private fun fromJscbJsToRecord(mBdJsData: CbfyApiResponse): TranResultZhYue? {
        var result: TranResultZhYue? = null
        if (mBdJsData.status == 1 && !TextUtils.isEmpty(mBdJsData.content)) {
            var contentStr = jiemi(mBdJsData.content!!)
            contentStr = contentStr.substringBeforeLast("}") + "}"
            val moshi = Moshi.Builder().build()
            val adapter = moshi.adapter(CbfyResult::class.java)
            val mCbfyResult = adapter.fromJson(contentStr)
            LogUtil.DefalutLog("contentStr：$contentStr")
            mCbfyResult?.let {
                result = TranResultZhYue(it.out, Setings.q, Setings.to)
            }
        }
        LogUtil.DefalutLog("fromJscbFyToRecord")
        return result
    }

    private fun jscbJsSign(text: String): String {
        val signStr = "6key_web_new_fanyi6dVjYLFyzfkFkk$text"
        val hash = md5Hash(signStr)
        val q = hash.substring(0, 16)
        val key = "L4fBtD5fLC9FQw22".toByteArray()
        val needSign = PKCS7Padding(q.toByteArray(), 16)
        val signedStr = encryptECB(needSign, key)
        val ciphertextBase64 = Base64.encodeToString(signedStr, Base64.DEFAULT)
        return ciphertextBase64
    }

    private fun jiemi(text: String): String {
        val key = "aahc3TfyfCEmER33".toByteArray()
        val encryptedBytes = Base64.decode(text, Base64.DEFAULT)
        val decrypted = decryptEcb(encryptedBytes, key)
        return String(decrypted)
    }

    private fun decryptEcb(encryptedBytes: ByteArray, key: ByteArray): ByteArray {
        val cipher = Cipher.getInstance("AES/ECB/NoPadding")
        cipher.init(Cipher.DECRYPT_MODE, SecretKeySpec(key, "AES"))
        return cipher.doFinal(encryptedBytes)
    }

    private fun encryptECB(src: ByteArray, key: ByteArray): ByteArray {
        val cipher = Cipher.getInstance("AES/ECB/NoPadding")
        cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(key, "AES"))
        return cipher.doFinal(src)
    }

    private fun md5Hash(text: String): String {
        val bytes = MessageDigest.getInstance("MD5").digest(text.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun PKCS7Padding(ciphertext: ByteArray, blockSize: Int): ByteArray {
        val padding = blockSize - (ciphertext.size % blockSize)
        return ciphertext + ByteArray(padding) { padding.toByte() }
    }




    @Throws(Exception::class)
    private fun fromJscbApiToRecord(mBdJsData: JscbApiData?): Record? {
        var result: Record? = null
        val sb = StringBuilder()
        if (mBdJsData != null && mBdJsData.errno == 0) {
            if (mBdJsData.baesInfo?.translate_type == 1) {
                if (mBdJsData.baesInfo?.symbols?.isNotEmpty() == true) {
                    val mSymbol = mBdJsData.baesInfo.symbols[0]
                    var symbolStr = ""
                    if (mSymbol.ph_en?.isNotBlank() == true) {
                        symbolStr += "英[" + mSymbol.ph_en + "]"
                    }
                    if (mSymbol.ph_am?.isNotBlank() == true) {
                        symbolStr += " 美[" + mSymbol.ph_am + "]"
                    }
                    if (symbolStr.isNotBlank()) {
                        sb.append(symbolStr)
                        sb.append("\n")
                    }
                    if (mSymbol.parts?.isNotEmpty() == true) {
                        for (part in mSymbol.parts) {
                            var partStr = ""
                            if (part.part?.isNotBlank() == true) {
                                if (StringUtils.isAllEnglish(part.part)) {
                                    partStr = part.part + " "
                                }
                            }
                            if (part.means?.isNotEmpty() == true) {
                                var meanStr = ""
                                for (mean in part.means) {
                                    meanStr += "$mean；"
                                }
                                if (meanStr.isNotBlank()) {
                                    meanStr = meanStr.substring(0, meanStr.length - 1)
                                    partStr += meanStr
                                }
                            }
                            if (partStr.isNotBlank()) {
                                sb.append(partStr)
                                sb.append("\n")
                            }
                        }
                    }
                    if (mBdJsData.baesInfo.exchange != null) {
                        var exresult = ""
                        if (mBdJsData.baesInfo.exchange.word_pl?.isNotEmpty() == true) {
                            exresult += "复数:" + mBdJsData.baesInfo.exchange.word_pl[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_third?.isNotEmpty() == true) {
                            exresult += "  第三人称单数:" + mBdJsData.baesInfo.exchange.word_third[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_past?.isNotEmpty() == true) {
                            exresult += "  过去式:" + mBdJsData.baesInfo.exchange.word_past[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_ing?.isNotEmpty() == true) {
                            exresult += "  现在分词:" + mBdJsData.baesInfo.exchange.word_ing[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_done?.isNotEmpty() == true) {
                            exresult += "  过去分词:" + mBdJsData.baesInfo.exchange.word_done[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_er?.isNotEmpty() == true) {
                            exresult += "  比较级:" + mBdJsData.baesInfo.exchange.word_er[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_est?.isNotEmpty() == true) {
                            exresult += "  最高级:" + mBdJsData.baesInfo.exchange.word_est[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_noun?.isNotEmpty() == true) {
                            exresult += "  名词:" + mBdJsData.baesInfo.exchange.word_noun[0]
                        }
                        if (exresult.isNotBlank()) {
                            sb.append(exresult.trim())
                            sb.append("\n")
                        }
                    }
                    var resultStr = sb.toString().trim()
                    if (resultStr.isNotEmpty()) {
                        result = Record(resultStr, Setings.q)
                    }
                }
            } else if (mBdJsData.baesInfo?.translate_result?.isNotBlank() == true) {
                result = Record(mBdJsData.baesInfo.translate_result, Setings.q)
            }
        }
        LogUtil.DefalutLog("fromJscbJsToRecord")
        return result
    }

    private fun getParseYoudaoWebHtml(html: String): Record? {
        val sb = StringBuilder()
        val sb_play = StringBuilder()
        var mrecord: Record? = null
        val doc = Jsoup.parse(html)
//        val error = doc.select("div.error-wrapper").first()
//        if (error != null) {
////            LogUtil.DefalutLog(error.text());
//            return null
//        }
        val feedback = doc.select("div.feedback").first()
        if (feedback != null) {
//            LogUtil.DefalutLog(feedback.text());
            return null
        }
        val symblo = doc.select("h2.wordbook-js > div.baav").first()
        if (symblo != null) {
            var text = symblo.text().trim()
            if (text.isNotEmpty() && text.contains("[")) {
                TranslateUtil.addContent(symblo, sb)
            }
        }
        val translate = doc.select("div#phrsListTab > div.trans-container").first()
        if (translate != null) {
            val lis = translate.getElementsByTag("ul").first()
            if (lis != null) {
                for (li in lis.children()) {
                    TranslateUtil.addContentAll(li, sb, sb_play)
                }
            }
            val p = translate.select("p.additional").first()
            if (p != null) {
                TranslateUtil.addContentAll(p, sb, sb_play)
            }
        }
        val fanyiToggle = doc.select("div#fanyiToggle").first()
        if (fanyiToggle != null) {
            val lis = fanyiToggle.getElementsByTag("p")
            if (lis != null && lis.size > 1) {
                TranslateUtil.addContentAll(lis[1], sb, sb_play)
            }
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getParseBingyingWebHtml(html: String): Record? {
        LogUtil.DefalutLog("---getParseBingyingWebHtml---")
        var mrecord: Record? = null
        val sb = java.lang.StringBuilder()
        val sb_play = java.lang.StringBuilder()
        val doc = Jsoup.parse(html)
        val smt_hw = doc.select("div.smt_hw").first()
        if (smt_hw != null) {
            val p1_11 = doc.select("div.p1-11").first()
            if (p1_11 != null) {
                TranslateUtil.addContentAll(p1_11, sb, sb_play)
            }
        }
        val symblo = doc.select("div.hd_p1_1").first()
        if (symblo != null) {
            TranslateUtil.addContent(symblo, sb)
        }
        val translates = doc.select("div.qdef > ul > li")
        if (translates != null && translates.size > 0) {
            for (li in translates) {
                var content = li.text().trim()
                if (content.contains("网络")) {
                    content = content.replace("网络", "网络：")
                }
                sb.append(content)
                sb.append("\n")
                sb_play.append(content)
                sb_play.append(",")
            }
        }
        val fusu = doc.select("div.qdef > div.hd_div1 > div.hd_if").first()
        if (fusu != null) {
            TranslateUtil.addContentAll(fusu, sb, sb_play)
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getParseHjiangWebHtml(html: String): Record? {
        val sb = StringBuilder()
        val sb_play = StringBuilder()
        var mrecord: Record? = null
        val doc = Jsoup.parse(html)
        val error = doc.select("div.word-notfound").first()
        if (error != null) {
            LogUtil.DefalutLog(error.text())
            return null
        }
        val symblo = doc.select("div.word-info > div.pronounces").first()
        if (symblo != null) {
            TranslateUtil.addContent(symblo, sb)
        }
        val translate = doc.select("div.simple").first()
        if (translate != null) {
            for (li in translate.children()) {
                TranslateUtil.addContentAll(li, sb, sb_play)
            }
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getSortData(map: Map<String, String>?): String {
        var result = ""
        if (map != null) {
            for ((key, value) in map) {
                result += "$key=$value&"
            }
            result += "app_key=" + Setings.QQAPPKEY
            result = MD5.encode(result).uppercase(Locale.getDefault())
        }
        return result
    }

    fun setBdJsApiKeys(jsonStr: String) {
        try {
            var mBdJsKeyData = Gson().fromJson(jsonStr, BdJsKeyData::class.java)
            if (mBdJsKeyData != null) {
                Setings.BdJsCookie = mBdJsKeyData.BdJsCookie
                Setings.BdJsOrigin = mBdJsKeyData.BdJsOrigin
                Setings.BdJsReferer = mBdJsKeyData.BdJsReferer
                Setings.BdJsSimpleMeansFlag = mBdJsKeyData.BdJsSimpleMeansFlag
                Setings.BdJsToken = mBdJsKeyData.BdJsToken
                Setings.BdJsTranstype = mBdJsKeyData.BdJsTranstype
                Setings.BdJsdomain = mBdJsKeyData.BdJsdomain
                Setings.BdJsSign1 = mBdJsKeyData.BdJsSign1
                Setings.BdJsSign2 = mBdJsKeyData.BdJsSign2
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //examination task
    suspend fun examFromJscbJs(): JscbJsData? {
        LogUtil.DefalutLog("examFromJscbJs")
        var result: JscbJsData? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbJsTranBaseUrl)
            val timestamp: String = System.currentTimeMillis().toString()
            val q = URLEncoder.encode(Setings.q, "utf-8")
            val params = "61000006$timestamp$q"
            val sign = KSettings.getJscbJsSign(params)
            val mResponseBody = service.tranByJscbJs(q, sign, timestamp)
            val responseStr = mResponseBody.body()?.string()
            if (!TextUtils.isEmpty(responseStr)) {
                result = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, JscbJsData::class.java)
                LogUtil.DefalutLog("Result---examFromJscbJs")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }
}