package com.messi.languagehelper.util

import android.text.TextUtils
import android.util.Base64

import com.messi.languagehelper.bean.*
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.httpservice.RetrofitBuilder
import com.mzxbkj.baselibrary.util.BaseSetings
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.zip.CRC32

object VideoParser {

    suspend fun loadVideoTask(url: String, vid: String = ""): PVideoResult? =
        withContext(Dispatchers.IO) {
            var resultData: PVideoResult? = null
            if ("bilibili" in url) {
                resultData = parseBilibiliWebSource(url)
                if ((resultData != null && TextUtils.isEmpty(resultData.url))
                    || resultData == null
                ) {
                    resultData = loadVideoXBKJ(url, vid)
                }
            } else {
                if (!TextUtils.isEmpty(vid)) {
                    resultData = parseToutiaoFromVid(vid)
                    if (TextUtils.isEmpty(resultData.url)) {
                        resultData = loadVideoXBKJ(url, vid)
                    }
                } else {
                    loadVideoXBKJ(url, vid)
                }
            }
            resultData
        }

    private fun parseBilibiliWebSource(url: String): PVideoResult? {
        LogUtil.DefalutLog("VideoParser---parseBilibiliWebSource")
        var resultData: PVideoResult? = null
        val mResponse = LanguagehelperHttpClient.get(url)
        if (mResponse != null && mResponse.isSuccessful) {
            val data = mResponse.body
            if (data != null) {
                val responseString = data.string()
                if (!TextUtils.isEmpty(responseString)) {
                    resultData = parseFromBiliPlayInfo(responseString)
                    if (TextUtils.isEmpty(resultData.url)) {
                        resultData = parseFromInitialState(responseString)
                    }
                }
            }
        }
        return resultData
    }

    private fun parseFromBiliPlayInfo(source: String): PVideoResult {
        val regex = "window.__playinfo__=(.*?)</script>".toRegex()
        val matched = regex.find(source)?.value
        val jsonStr = matched?.replace("window.__playinfo__=", "")
        val jsonResult = jsonStr?.replace("</script>", "")
        return parseBiliMediaJson(jsonResult)
    }

    private fun parseBiliMediaJson(jsonResult: String?): PVideoResult {
        val resultData = PVideoResult()
        if (!TextUtils.isEmpty(jsonResult)) {
            val moshi = Moshi.Builder().build()
            val jsonAdapter: JsonAdapter<BiliPlayInfo> =
                moshi.adapter(BiliPlayInfo::class.java)
            val mResult = jsonAdapter.fromJson(jsonResult!!)
            var videoList: List<BVideo>? = null
            var audioList: List<BAudio>? = null
            mResult?.let { rt ->
                rt.data?.let { dt ->
                    dt.dash?.let {
                        videoList = it.video
                        audioList = it.audio
                        it.duration?.let { dur ->
                            resultData.duration = dur.toDouble()
                        }
                    }
                }
            }
            videoList?.sortedByDescending { it.id }
            audioList?.sortedByDescending { it.id }
            if (!videoList.isNullOrEmpty()) {
                if (!TextUtils.isEmpty(videoList!![0].base_url)) {
                    resultData.url = videoList!![0].base_url
                } else if (!TextUtils.isEmpty(videoList!![0].baseUrl)) {
                    resultData.url = videoList!![0].baseUrl
                }
                LogUtil.DefalutLog("resultData.vid:${videoList!![0].id}")
            }
            if (!audioList.isNullOrEmpty()) {
                if (!TextUtils.isEmpty(audioList!![0].base_url)) {
                    resultData.mp3Url = audioList!![0].base_url
                } else if (!TextUtils.isEmpty(audioList!![0].baseUrl)) {
                    resultData.mp3Url = audioList!![0].baseUrl
                }
                LogUtil.DefalutLog("resultData.aid:${audioList!![0].id}")
            }
        }
        return resultData
    }

    private fun parseFromInitialState(source: String): PVideoResult {
        LogUtil.DefalutLog("VideoParser---parseFromInitialState")
        var resultData = PVideoResult()
        val biliState = parseInitialState(source)
        if (biliState != null) {
            var aid = ""
            var bvid = ""
            var cid = ""
            biliState.aid?.let {
                aid = "$it"
            }
            biliState.bvid?.let {
                bvid = it
            }
            biliState.cidMap?.let {
                LogUtil.DefalutLog("aid:$aid---aJson:${it}")
                if (it.containsKey(aid)) {
                    val aJson = it[aid]
                    val cids = aJson?.get("cids") as Map<String, Double>
                    val cidDou = cids[cids.keys.first()]
                    cid = "${cidDou?.toInt()}"
                    LogUtil.DefalutLog("aJson:$aJson")
                    LogUtil.DefalutLog("cids:$cids")
                    LogUtil.DefalutLog("cid:${cid}")
                }
            }
            if (cid.isNotEmpty() && bvid.isNotEmpty()) {
                val api =
                    "https://api.bilibili.com/x/player/playurl?cid=$cid&bvid=$bvid&qn=64&type=&otype=json&fourk=1&fnver=0&fnval=2000"
                resultData = parseBiliApi(api)
            }
        }
        return resultData
    }

    private fun parseBiliApi(url: String): PVideoResult {
        LogUtil.DefalutLog("VideoParser---parseBiliApi")
        var resultData = PVideoResult()
        val mResponse = LanguagehelperHttpClient.get(url)
        if (mResponse != null && mResponse.isSuccessful && mResponse.body != null) {
            val responseString = mResponse.body!!.string()
            if (!TextUtils.isEmpty(responseString)) {
                resultData = parseBiliMediaJson(responseString)
                LogUtil.DefalutLog("VideoParser---parseBiliApi:${resultData}")
            }
        }
        return resultData
    }

    private fun parseInitialState(source: String): BiliInitialState? {
        var resultData: BiliInitialState? = null
        try {
            val regex = "window.__INITIAL_STATE__=(.*?);[()]".toRegex()
            val matched = regex.find(source)?.value
            val jsonStr = matched?.replace("window.__INITIAL_STATE__=", "")
            val jsonResult = jsonStr?.substring(0, jsonStr.indexOf(";"))
            if (!TextUtils.isEmpty(jsonResult)) {
                val moshi = Moshi.Builder().build()
                val jsonAdapter: JsonAdapter<BiliInitialState> =
                    moshi.adapter(BiliInitialState::class.java)
                resultData = jsonAdapter.fromJson(jsonResult!!)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return resultData
    }

    private fun parseToutiaoFromVid(vid: String): PVideoResult {
        LogUtil.DefalutLog("VideoParser---parseToutiaoFromVid:$vid")
        var videoUrl = ""
        try {
//            String pattern = "\"vid\":\"(\\w*)\"";
            val randint = StringUtils.getRandomString(16)
            val videoid = "/video/urls/v/1/toutiao/mp4/$vid?r=$randint"
            val crc32 = CRC32()
            crc32.update(videoid.toByteArray())
            val checksum = crc32.value
            videoUrl = "http://i.snssdk.com$videoid&s=$checksum"
            LogUtil.DefalutLog("videoid:$videoid-videoUrl:$videoUrl")
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return parseToutiaoApi(videoUrl)
    }

    private fun parseToutiaoApi(url: String): PVideoResult {
        LogUtil.DefalutLog("VideoParser---parseToutiaoApi:$url")
        val resultData = PVideoResult()
        try {
            val mResponse = LanguagehelperHttpClient.get(url)
            if (mResponse != null && mResponse.isSuccessful && mResponse.body != null) {
                val responseString = mResponse.body!!.string()
                if (!TextUtils.isEmpty(responseString)) {
                    val moshi = Moshi.Builder().build()
                    val jsonAdapter = moshi.adapter(ToutiaoVideoBean::class.java)
                    val toutiaoVideo = jsonAdapter.fromJson(responseString)
                    if (toutiaoVideo != null && toutiaoVideo.code == 0) {
                        val videoUrl = String(
                            Base64.decode(
                                toutiaoVideo.data.video_list.video_1.main_url.toByteArray(),
                                Base64.DEFAULT
                            )
                        )
                        if (!TextUtils.isEmpty(videoUrl)) {
                            resultData.url = videoUrl
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return resultData
    }

    private suspend fun loadVideoXBKJ(url: String, vid: String = ""): PVideoResult? {
        LogUtil.DefalutLog("VideoParser---loadVideoXBKJ")
        var result: PVideoResult? = null
        try {
            val timestamp = System.currentTimeMillis().toString()
            val platform = SystemUtil.platform
            val network = SystemUtil.network
            val sign = SignUtil.getMd5Sign(BaseSetings.PVK, timestamp, url, platform, network)
            val call = RetrofitBuilder.vService.getPVideoApi(
                url,
                network,
                platform,
                sign,
                timestamp,
                0,
                vid
            )
            if (call.isSuccessful) {
                result = call.body()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    fun parseToutiaoWebApi(url: String): PVideoResult {
        val resultData = PVideoResult()
        val mResponse = LanguagehelperHttpClient.get(url)
        if (mResponse != null && mResponse.isSuccessful && mResponse.body != null) {
            val responseString = mResponse.body!!.string()
            if (!TextUtils.isEmpty(responseString)) {
                try {
                    val start = responseString.indexOf("{")
                    val end = responseString.lastIndexOf("}")
                    if (start > 0 && end > 0) {
                        val jstr = responseString.substring(start, end + 1)
                        LogUtil.DefalutLog("parseToutiaoWebApi---jstr:$jstr")
                        if (JsonParser.isJson(jstr)) {
                            val moshi = Moshi.Builder().build()
                            val jsonAdapter = moshi.adapter(ToutiaoWebRootBean::class.java)
                            val result = jsonAdapter.fromJson(jstr)
                            if (result != null && result.data != null && result.data.video_list != null && result.data.video_list.video_1 != null) {
                                resultData.url = result.data.video_list.video_1.main_url
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        return resultData
    }
}
//           https://api.bilibili.com/x/player/playurl?cid=%s&qn=64&type=&otype=json&fourk=1&bvid=%s&fnver=0&fnval=976" % (cid, bvid)
//# pagelist https://api.bilibili.com/x/player/pagelist?bvid=BV1Ey4y1B7ud&jsonp=jsonp
//# detail   https://api.bilibili.com/x/player/playurl?cid=248028725&bvid=BV1Ey4y1B7ud&qn=64&type=&otype=json&fourk=1&fnver=0&fnval=2000
//# related  https://api.bilibili.com/x/web-interface/view/detail?bvid=BV1Ey4y1B7ud&aid=800081787&need_operation_card=1&web_rm_repeat=&need_elec=1&out_referer=
//# related  https://api.bilibili.com/x/web-interface/archive/related?aid=800081787&need_operation_card=1&web_rm_repeat=1

//if "group/" in url:
//gids = url.split("group/")
//if len(gids) >= 2:
//gid = gids[1]
//elif "www.ixigua.com/i" in url:
//gid = url.replace("https://www.ixigua.com/i", "")
//elif "www.ixigua.com/" in url:
//gid = url.replace("https://www.ixigua.com/", "")
//
//if "?logTag" in gid:
//gids = gid.split("?")
//if len(gids) > 0:
//gid = gids[0]
//if "/" in gid:
//gid = gid.replace("/", "")
//# print(gid)
//if gid:
//api = "https://www.ixigua.com/api/public/videov2/brief/details?group_id=" + gid