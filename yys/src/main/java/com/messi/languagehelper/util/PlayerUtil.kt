package com.messi.languagehelper.util

import android.content.Context
import com.messi.languagehelper.aidl.IXBPlayer
import com.messi.languagehelper.box.NDetail
import com.messi.languagehelper.service.MusicServiceConnection
import com.messi.languagehelper.service.PlayerService
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types

object PlayerUtil {

    var appAlive = true
    var musicServiceConnection: MusicServiceConnection? = null
    var lastPlayer = ""

    @JvmField
    var musicSrv: IXBPlayer? = null

    val countdownNum: Int
        get() {
            try {
                if (musicServiceConnection != null) {
                    return musicServiceConnection!!.countDown
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return -3
        }

    var playSpeed: Float
        get() {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.playSpeed
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return 1.0f
        }
        set(speed) {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.setPlaySpeed(speed)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    val albumId: String
        get() {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.albumId
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return ""
        }

    val boutiqueCode: String
        get() {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.boutiqueCode
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return ""
        }

    fun playNext(): String {
        try {
            if (musicSrv != null) {
                return musicSrv!!.playNext()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun playPrevious() {
        try {
            if (musicServiceConnection != null) {
                musicServiceConnection!!.transportControls.skipToPrevious()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun playIndex(index: Int) {
        try {
            if (musicServiceConnection != null) {
                musicServiceConnection!!.playIndex(index)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setCountdownNumber(num: Int) {
        try {
            if (musicServiceConnection != null) {
                musicServiceConnection!!.setCount(num)
                LogUtil.DefalutLog("setCountdownNumber:${musicServiceConnection!!.countDown}")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    val currentPosition: Int
        get() {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.currentPosition
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return 0
        }

    val duration: Int
        get() {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.duration
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return 0
        }

    val order: Int
        get() {
            try {
                if (musicSrv != null) {
                    return musicSrv!!.order
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return 0
        }

    fun pause() {
        try {
            if (musicServiceConnection != null) {
                musicServiceConnection!!.transportControls.pause()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun pause(context: Context?) {
        try {
            if (musicServiceConnection != null) {
                musicServiceConnection!!.transportControls.pause()
            }
            context?.let {
                NotificationUtil.sendBroadcast(it, PlayerService.action_restart)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun isPlaying(): Boolean {
        try {
            if (musicSrv != null) {
                return musicSrv!!.MPlayerIsPlaying()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    fun play() {
        try {
            if (musicServiceConnection != null) {
                musicServiceConnection!!.transportControls.play()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun seekTo(position: Int) {
        try {
            if (musicSrv != null) {
                musicSrv!!.MPlayerSeekTo(position)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @JvmStatic
    fun isSameMedia(song: NDetail?): Boolean {
        try {
            if (musicSrv != null && song != null) {
                return musicSrv!!.MPlayerIsSameMp3(song.object_id)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    fun initAndPlay(song: NDetail?) {
        try {
            LogUtil.DefalutLog("IPlayerUtil---initAndPlay")
            val moshi = Moshi.Builder().build()
            val jsonAdapter = moshi.adapter(NDetail::class.java)
            val data = jsonAdapter.toJson(song)
            musicSrv!!.initAndPlay(data, true, 0)
        } catch (e: Exception) {
            LogUtil.DefalutLog("RemoteException---initAndPlay")
            e.printStackTrace()
        }
    }

    @JvmStatic
    fun initAndPlay(song: NDetail?, isPlayList: Boolean, position: Long) {
        try {
            LogUtil.DefalutLog("IPlayerUtil---initAndPlay---musicSrv")
            val moshi = Moshi.Builder().build()
            val jsonAdapter = moshi.adapter(NDetail::class.java)
            val data = jsonAdapter.toJson(song)
            musicSrv!!.initAndPlay(data, isPlayList, position)
        } catch (e: Exception) {
            LogUtil.DefalutLog("RemoteException---initAndPlay--isPlayList")
            e.printStackTrace()
        }
    }

    fun initList(list: List<NDetail>?, position: Int) {
        try {
            LogUtil.DefalutLog("IPlayerUtil---initPlayList---musicSrv")
            if (list != null) {
                val pItem = list[position]
                val nList = getMp3List(list)
                val nPos = nList.indexOf(pItem)
                val moshi = Moshi.Builder().build()
                val type = Types.newParameterizedType(List::class.java, NDetail::class.java)
                val jsonAdapter: JsonAdapter<List<NDetail>> = moshi.adapter(type)
                val lists = jsonAdapter.toJson(nList)
                musicSrv!!.initPlayList(lists, nPos)
            }
        } catch (e: Exception) {
            LogUtil.DefalutLog("RemoteException---initPlayList")
            e.printStackTrace()
        }
    }

    fun initList(list: List<NDetail>?, position: Int, pos: Int, orderBy: String?) {
        try {
            LogUtil.DefalutLog("IPlayerUtil---initPlayList---musicSrv")
            if (list != null) {
                val pItem = list[position]
                val nList = getMp3List(list)
                val nPos = nList.indexOf(pItem)
                val moshi = Moshi.Builder().build()
                val type = Types.newParameterizedType(List::class.java, NDetail::class.java)
                val jsonAdapter: JsonAdapter<List<NDetail>> = moshi.adapter(type)
                val lists = jsonAdapter.toJson(nList)
                musicSrv!!.initPlayListWithOrder(lists, nPos, pos.toLong(), orderBy)
            }

        } catch (e: Exception) {
            LogUtil.DefalutLog("RemoteException---initPlayList")
            e.printStackTrace()
        }
    }

    fun initVideoBgPlayList(list: List<NDetail>, position: Int, pos: Long, orderBy: String?) {
        try {
            LogUtil.DefalutLog("IPlayerUtil---initList-size:${list.size}-position:$position-pos:$pos-orderBy:$orderBy")
            if (list.isNotEmpty()) {
                val pItem = list[position]
                val nList = getVideoList(list)
                val nPos = nList.indexOf(pItem)
                val moshi = Moshi.Builder().build()
                val type = Types.newParameterizedType(List::class.java, NDetail::class.java)
                val jsonAdapter: JsonAdapter<List<NDetail>> = moshi.adapter(type)
                val lists = jsonAdapter.toJson(nList)
                musicSrv!!.initPlayListWithOrder(lists, nPos, pos, orderBy)
            }
        } catch (e: Exception) {
            LogUtil.DefalutLog("RemoteException---initPlayList:${e.printStackTrace()}")
            e.printStackTrace()
        }
    }

    private fun getVideoList(list: List<NDetail>): List<NDetail> {
        val nList: MutableList<NDetail> = java.util.ArrayList()
        for (i in list.indices) {
            if ("video" == list[i].type) {
                nList.add(list[i])
            }
        }
        return nList
    }

    private fun getMp3List(list: List<NDetail>): List<NDetail> {
        val nList: MutableList<NDetail> = ArrayList()
        for (i in list.indices) {
            if ("audio" == list[i].type || "mp3" == list[i].type) {
                nList.add(list[i])
            }
        }
        return nList
    }

    val currentItem: NDetail?
        get() {
            var currentItem: NDetail? = null
            try {
                var str: String? = ""
                if (musicSrv != null) {
                    str = musicSrv!!.currentItem
                    if (JsonParser.isJson(str)) {
                        val moshi = Moshi.Builder().build()
                        val jsonAdapter = moshi.adapter(NDetail::class.java)
                        currentItem = jsonAdapter.fromJson(str)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return currentItem
        }
}