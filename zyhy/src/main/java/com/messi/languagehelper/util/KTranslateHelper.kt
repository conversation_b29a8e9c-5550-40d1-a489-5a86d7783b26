package com.messi.languagehelper.util

import android.text.TextUtils
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.messi.languagehelper.bean.*
import com.messi.languagehelper.box.Record
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.httpservice.RetrofitBuilder
import com.mzxbkj.baselibrary.util.BaseSetings
import com.mzxbkj.baselibrary.util.LogUtil
import com.mzxbkj.baselibrary.util.SignUtil
import com.squareup.moshi.Moshi
import okhttp3.Request
import org.jsoup.Jsoup
import java.net.URLEncoder
import java.util.Locale

object KTranslateHelper {

    //  xbkj,bdjs,jscbjs,youdaoweb,jscbapi,bingweb,hujiangweb,jscbfy,qqfyj,hujiangapi,youdaoapi#youdaoweb,bdjs,jscbjs,xbkj,jscbfy,bingweb,hujiangweb,youdaoapi,hujiangapi
    private var OrderTran =
        "xbkj,youdaoweb,bingweb,jscbjs,hujiangweb,hujiangapi,youdaoapi,bdjs"

    //    xbkj,youdaoweb,bingweb,jscbjs,hujiangweb,hujiangapi,youdaoapi,bdjs#youdaoweb,bingweb,hujiangweb,jscb,youdaoapi,hujiangapi,baidu
    private const val youdaoweb = "youdaoweb"
    private const val youdaoapi = "youdaoapi"
    private const val bingweb = "bingweb"
    private const val bdjs = "bdjs"
    private const val xbkj = "xbkj"
    private const val jscbjs = "jscbjs"
    private const val jscbfy = "jscbfy"
    private const val jscbapi = "jscbapi"
    private const val hujiangapi = "hujiangapi"
    private const val hujiangweb = "hujiangweb"
    private const val qqfyj = "qqfyj"
    private lateinit var tranOrder: MutableList<String>
    private var isInitYoudao = false

    fun initTranOrder(orderStr: String?) {
        LogUtil.Log("---initTranOrder---")
        try {
            if (!TextUtils.isEmpty(orderStr) && orderStr!!.contains("#")) {
                val keys = orderStr.split("#".toRegex()).toTypedArray()
                if (keys.size > 1) {
                    if (!TextUtils.isEmpty(keys[0])) {
                        OrderTran = keys[0]
                    }
                    if (!TextUtils.isEmpty(keys[1])) {
                        DictHelper.OrderDic = keys[1]
                    }
                }
            } else {
                dafultInitValue()
            }
            dafultInitOrder()
        } catch (e: Exception) {
            LogUtil.Log("initTranOrder---Exception")
            dafultInitValue()
            dafultInitOrder()
            e.printStackTrace()
        }
    }

    private fun dafultInitValue() {
        OrderTran =
            "xbkj,youdaoweb,bdjs,jscbjs,jscbapi,bingweb,hujiangweb,jscbfy,qqfyj,hujiangapi,youdaoapi"
        KDictHelper.OrderDic = "youdaoweb,bdjs,jscbjs,xbkj,bingweb,hujiangweb,youdaoapi,hujiangapi"
    }

    private fun dafultInitOrder() {
        tranOrder = OrderTran.split(",").toMutableList()
        KDictHelper.dictOrder = KDictHelper.OrderDic.split(",").toMutableList()
    }

    suspend fun doTranslateTask(): Record? {
        if (!::tranOrder.isInitialized) {
            initTranOrder("")
        }
        var mRecordResult: Record? = null
        try {
            for (method in tranOrder) {
                LogUtil.Log("DoTranslateByMethod---$method")
                when (method) {
                    youdaoweb -> {
                        mRecordResult = tranFromYDWebNew()
                    }

                    bdjs -> {
                        mRecordResult = tranFromBdJs()
                    }

                    jscbjs -> {
                        mRecordResult = tranFromJscbJs()
                    }

                    jscbapi -> {
                        mRecordResult = tranFromYDWebNew()
                    }

                    jscbfy -> {
                        mRecordResult = tranFromYDWeb()
                    }

                    xbkj -> {
                        mRecordResult = tranFromXBKJ()
                    }

                    youdaoapi -> {
                        mRecordResult = tranFromYDApi()
                    }

                    bingweb -> {
                        mRecordResult = tranFromBingWeb()
                    }

                    hujiangapi -> {
                        mRecordResult = tranFromHJApi()
                    }

                    hujiangweb -> {
                        mRecordResult = tranFromHjWeb()
                    }

                    qqfyj -> {
                        mRecordResult = tranFromXBKJ()
                    }
                }
                if (mRecordResult != null) break
            }
            //test
//            mRecordResult = tranFromBdJs()     //3
//            mRecordResult = tranFromYDWeb()    //2
//            mRecordResult = tranFromYDWebNew()    //1
//                tranFromJscbJs()   //2
//                tranFromJscbApi()  //bad
//                tranFromJscbFy()   //bad
//                tranFromYDApi()    //6
//                tranFromBingWeb()  //3
//                tranFromHJApi()    //5
//                tranFromHjWeb()    //4
//                tranFromQQFYJ()    //bad
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecordResult
    }

    private suspend fun tranFromXBKJ(): Record? {
        LogUtil.Log("tranFromXBKJ")
        var mRecord: Record? = null
        try {
            val timestamp = System.currentTimeMillis().toString()
            val platform = SystemUtil.platform
            val network = SystemUtil.network
            if (StringUtils.isEnglish(Setings.q)) {
                Setings.from = "en"
                Setings.to = "zh"
            } else {
                Setings.from = "zh"
                Setings.to = "en"
            }
            val sign = SignUtil.getMd5Sign(
                BaseSetings.TSLVK, timestamp, Setings.q,
                platform, network, Setings.from, Setings.to
            )
            val responseResult = RetrofitBuilder.tService.tranDict(
                Setings.q,
                Setings.from,
                Setings.to,
                network,
                platform,
                sign,
                0,
                timestamp
            )
            val mResult = responseResult.body()
//            LogUtil.DefalutLog(responseResult.message())
            if (mResult != null) {
                val tdResult = mResult.result
                if (tdResult != null && !TextUtils.isEmpty(tdResult.result)) {
                    var des = tdResult.result
                    if (!TextUtils.isEmpty(tdResult.symbol)) {
                        des = tdResult.symbol + "\n" + tdResult.result
                    }
                    mRecord = Record(des, Setings.q)
                    mRecord.ph_am_mp3 = tdResult.mp3_am
                    mRecord.ph_en_mp3 = tdResult.mp3_en
                    mRecord.backup1 = tdResult.result
                    LogUtil.Log("Result---tranFromXBKJ:$mRecord")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecord
    }

    private suspend fun tranFromBdJs(): Record? {
        LogUtil.Log("tranFromBdJs")
        //zh  en  ja  ko  fr  de  es
        var result: Record? = null
        try {
            var from = "zh"
            var to = "en"
            if (StringUtils.isEnglish(Setings.q)) {
                from = "en"
                to = "zh"
            }
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.BdJsTranBaseUrl)
            val sign = BDEncoder().encode(Setings.q)
            val mResponseBody = service.tranByBdJsApi(from, to, Setings.q, sign)
            val responseStr = mResponseBody.body()?.string()
            LogUtil.Log("BdJs-responseStr:$responseStr")
            if (!TextUtils.isEmpty(responseStr)) {
                val mBdJsData = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, BdJsData::class.java)
                result = fromBdJsToRecord(mBdJsData)
                LogUtil.Log("Result---tranFromBdJs:$result")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromJscbJs(): Record? {
        LogUtil.Log("tranFromJscbJs")
        var result: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbJsTranBaseUrl)
            val timestamp: String = System.currentTimeMillis().toString()
            val q = URLEncoder.encode(Setings.q, "utf-8")
            val params = "61000006$timestamp$q"
//            LogUtil.DefalutLog("params:$params")
            val sign = KSettings.getJscbJsSign(params)
//            LogUtil.DefalutLog("sign:$sign")
            val mResponseBody = service.tranByJscbJs(q, sign, timestamp)
            val responseStr = mResponseBody.body()?.string()
            LogUtil.Log("JscbJs:$responseStr")
            if (!TextUtils.isEmpty(responseStr)) {
                val mBdJsData = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, JscbJsData::class.java)
                result = fromJscbJsToRecord(mBdJsData)
                LogUtil.Log("Result---tranFromJscbJs:$result")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromJscbFy(): Record? {
        LogUtil.Log("tranFromJscbFy")
        var result: Record? = null
        try {
            var from = "zh"
            var to = "en"
            if (StringUtils.isEnglish(Setings.q)) {
                from = "en"
                to = "zh"
            }
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbFyBaseUrl)
            val sign = KSettings.getJscbFySign(Setings.q)
//            LogUtil.DefalutLog("sign:$sign")
            val mResponseBody = service.tranByJscbFy(sign, Setings.q, from, to)
            val mJscbFyData = mResponseBody.body()
            if (mJscbFyData != null) {
                if (mJscbFyData.status == 1 && !TextUtils.isEmpty(mJscbFyData.content?.out)) {
                    result = Record(mJscbFyData.content?.out, Setings.q)
                    LogUtil.Log("Result---tranFromJscbFy:$result")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromJscbApi(): Record? {
        LogUtil.Log("tranFromJscbApi")
        var result: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbApiBaseUrl)
            val mResponseBody = service.tranByJscbApi(Setings.q)
            val responseStr = mResponseBody.body()?.string()
            LogUtil.Log("JscbApi:$responseStr")
            if (!TextUtils.isEmpty(responseStr)) {
                val mBdJsData = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, JscbApiData::class.java)
                result = fromJscbApiToRecord(mBdJsData)
                LogUtil.Log("Result---tranFromJscbApi:$result")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromQQFYJ(): Record? {
        LogUtil.Log("tranFromQQFYJ")
        var result: Record? = null
        try {
            val time_stamp = (System.currentTimeMillis() / 1000).toString()
            val nonce_str = StringUtils.getRandomString(16)
            var source = "zh"
            var target = "en"
            if (StringUtils.isEnglish(Setings.q)) {
                source = "en"
                target = "zh"
            }
            val map = sortedMapOf(
                "app_id" to URLEncoder.encode(Setings.QQAPPID, "UTF-8"),
                "nonce_str" to URLEncoder.encode(nonce_str, "UTF-8"),
                "text" to URLEncoder.encode(Setings.q, "UTF-8"),
                "time_stamp" to URLEncoder.encode(time_stamp, "UTF-8"),
                "source" to URLEncoder.encode(source, "UTF-8"),
                "target" to URLEncoder.encode(target, "UTF-8")
            )
            val sign = getSortData(map)
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.QQTranFYJBase)
            val response = service.tranByQQFYJ(
                Setings.QQAPPID,
                time_stamp,
                sign,
                source,
                target,
                Setings.q,
                nonce_str
            )
            val data = response.body()
            if (data != null && data.ret == 0 && data.data != null) {
                result = Record(data.data.target_text, Setings.q)
                LogUtil.Log("Result---tranFromQQFYJ")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromYDWebNew(): Record? {
        LogUtil.Log("tranFromYDWebNew")
        var result: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.YoudaoWebNew)
            val response = service.tranByYDWebNew(word = Setings.q)
            val data = response.body()
            if (data != null) {
                val responseString = data.string()
                if (!TextUtils.isEmpty(responseString)) {
//                    LogUtil.DefalutLog("Result---tranFromYDWebNew:$responseString")
                    result = getParseYoudaoWebHtmlNew(responseString)
                    LogUtil.Log("Result---tranFromYDWebNew:$result")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromYDWeb(): Record? {
        LogUtil.Log("tranFromYDWeb")
        var result: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.YoudaoWeb)
            val response = service.tranByYDWeb(Setings.q)
            val data = response.body()
            if (data != null) {
                val responseString = data.string()
                if (!TextUtils.isEmpty(responseString)) {
                    result = getParseYoudaoWebHtml(responseString)
                    LogUtil.Log("Result---tranFromYDWeb:$result")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromBingWeb(): Record? {
        LogUtil.Log("tranFromBingWeb")
        var result: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.BingyingWebBase)
            val response = service.tranByBingWeb(Setings.q)
            val body = response.body()
            if (body != null) {
                val data = body.string()
                if (!TextUtils.isEmpty(data)) {
                    result = getParseBingyingWebHtml(data)
                    LogUtil.Log("Result---tranFromBingWeb:$result")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    private fun tranFromHjWeb(): Record? {
        LogUtil.Log("tranFromHjWeb")
        var result: Record? = null
        try {
            val request = Request.Builder()
                .url(Setings.HjiangWeb + StringUtils.replaceAll(Setings.q))
                .header("User-Agent", LanguagehelperHttpClient.Header)
                .header("Cookie", TranslateHelper.HJCookie)
                .build()
            val response = LanguagehelperHttpClient.get(request, null)
            if (response.isSuccessful) {
                val responseString = response.body?.string()
                if (!TextUtils.isEmpty(responseString)) {
                    result = getParseHjiangWebHtml(responseString.toString())
                    LogUtil.Log("Result---tranFromHjWeb:$result")
                }
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return result
    }

    private suspend fun tranFromYDApi(): Record? {
        LogUtil.Log("tranFromYDApi")
        var mRecord: Record? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.YoudaoApiBase)
            val response = service.tranByYDApi(Setings.q)
            val bean = response.body()
            if (bean != null && bean.errorCode == 0 && bean.translateResult != null) {
                val list = bean.translateResult
                if (list.isNotEmpty()) {
                    val item = list[0]
                    if (item != null && item.isNotEmpty()) {
                        val result = item[0]
                        if (result != null && !TextUtils.isEmpty(result.tgt)) {
                            mRecord = Record(result.tgt, Setings.q)
                            LogUtil.Log("Result---tranFromYDApi:$mRecord")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecord
    }

    private fun tranFromHJApi(): Record? {
        LogUtil.Log("tranFromHJApi")
        var mRecord: Record? = null
        try {
            val response = LanguagehelperHttpClient.postHjApi(null)
            if (response.isSuccessful) {
                val dataString = response.body?.string()
                mRecord = tran_hj_api(dataString.toString())
                LogUtil.Log("Result---tranFromHJApi:$mRecord")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mRecord
    }

    @Throws(Exception::class)
    private fun tran_hj_api(mResult: String): Record? {
        var currentDialogBean: Record? = null
        if (!TextUtils.isEmpty(mResult)) {
            if (JsonParser.isJson(mResult)) {
                val moshi = Moshi.Builder().build()
                val adapter = moshi.adapter(HjTranBean::class.java)
                val mHjTranBean = adapter.fromJson(mResult)
                if (mHjTranBean != null && mHjTranBean.status == 0 && mHjTranBean.data != null && !TextUtils.isEmpty(
                        mHjTranBean.data.content
                    )
                ) {
                    currentDialogBean = Record(mHjTranBean.data.content, Setings.q)
                }
            }
        }
        LogUtil.Log("tran_hj_api:$currentDialogBean")
        return currentDialogBean
    }

    @Throws(Exception::class)
    private fun fromBdJsToRecord(mBdJsData: BdJsData): Record? {
        var result: Record? = null
        val sb = StringBuilder()
        if (StringUtils.isAllEnglish(Setings.q)) {
            if (mBdJsData.dict_result?.simple_means?.symbols?.isNotEmpty() == true) {
                val mSymbol = mBdJsData.dict_result.simple_means.symbols[0]
                var symbolStr = ""
                if (mSymbol.ph_en?.isNotBlank() == true) {
                    symbolStr += "英[" + mSymbol.ph_en + "]"
                }
                if (mSymbol.ph_am?.isNotBlank() == true) {
                    symbolStr += " 美[" + mSymbol.ph_am + "]"
                }
                if (symbolStr.isNotBlank()) {
                    sb.append(symbolStr)
                    sb.append("\n")
                }
                if (mSymbol.parts?.isNotEmpty() == true) {
                    for (part in mSymbol.parts) {
                        var partStr = ""
                        if (part.part?.isNotBlank() == true) {
                            if (StringUtils.isAllEnglish(part.part)) {
                                partStr = part.part + " "
                            }
                        }
                        if (part.means?.isNotEmpty() == true) {
                            var meanStr = ""
                            for (mean in part.means) {
                                meanStr += "$mean；"
                            }
                            if (meanStr.isNotBlank()) {
                                meanStr = meanStr.substring(0, meanStr.length - 1)
                                partStr += meanStr
                            }
                        }
                        if (partStr.isNotBlank()) {
                            sb.append(partStr)
                            sb.append("\n")
                        }
                    }
                }
                if (mBdJsData.dict_result.simple_means.exchange != null) {
                    var exresult = ""
                    if (mBdJsData.dict_result.simple_means.exchange.word_pl?.isNotEmpty() == true) {
                        exresult += "复数:" + mBdJsData.dict_result.simple_means.exchange.word_pl[0]
                    }
                    if (mBdJsData.dict_result.simple_means.exchange.word_third?.isNotEmpty() == true) {
                        exresult += "  第三人称单数:" + mBdJsData.dict_result.simple_means.exchange.word_third[0]
                    }
                    if (mBdJsData.dict_result.simple_means.exchange.word_past?.isNotEmpty() == true) {
                        exresult += "  过去式:" + mBdJsData.dict_result.simple_means.exchange.word_past[0]
                    }
                    if (mBdJsData.dict_result.simple_means.exchange.word_ing?.isNotEmpty() == true) {
                        exresult += "  现在分词:" + mBdJsData.dict_result.simple_means.exchange.word_ing[0]
                    }
                    if (mBdJsData.dict_result.simple_means.exchange.word_done?.isNotEmpty() == true) {
                        exresult += "  过去分词:" + mBdJsData.dict_result.simple_means.exchange.word_done[0]
                    }
                    if (mBdJsData.dict_result.simple_means.exchange.word_er?.isNotEmpty() == true) {
                        exresult += "  比较级:" + mBdJsData.dict_result.simple_means.exchange.word_er[0]
                    }
                    if (mBdJsData.dict_result.simple_means.exchange.word_est?.isNotEmpty() == true) {
                        exresult += "  最高级:" + mBdJsData.dict_result.simple_means.exchange.word_est[0]
                    }
                    if (exresult.isNotBlank()) {
                        sb.append(exresult.trim())
                        sb.append("\n")
                    }
                }
                if (mBdJsData.dict_result.simple_means.tags?.core?.isNotEmpty() == true) {
                    var tag = ""
                    for (mTag in mBdJsData.dict_result.simple_means.tags.core) {
                        tag += "$mTag/"
                    }
                    if (tag.isNotBlank()) {
                        tag = tag.substring(0, tag.length - 1)
                        sb.append(tag.trim())
                        sb.append("\n")
                    }
                }

                val resultStr = sb.toString().trim()
                if (resultStr.isNotEmpty()) {
                    result = Record(resultStr, Setings.q)
                }
            } else if (mBdJsData.trans_result?.data?.isNotEmpty() == true) {
                for (item in mBdJsData.trans_result.data) {
                    sb.append(item.dst)
                    sb.append("\n")
                }
                result = Record(sb.toString().trim(), Setings.q)
            }
        } else {
            if (mBdJsData.trans_result?.data?.isNotEmpty() == true) {
                for (item in mBdJsData.trans_result.data) {
                    sb.append(item.dst)
                    sb.append("\n")
                }
                result = Record(sb.toString().trim(), Setings.q)
            }
        }
        LogUtil.Log("tran_fromBdJs:$result")
        return result
    }

    @Throws(Exception::class)
    private fun fromJscbJsToRecord(mBdJsData: JscbJsData): Record? {
        var result: Record? = null
        val sb = StringBuilder()
        if (mBdJsData.status == 1) {
            if (mBdJsData.message?.baesInfo?.translate_type == 1) {
                if (mBdJsData.message.baesInfo.symbols?.isNotEmpty() == true) {
                    val mSymbol = mBdJsData.message.baesInfo.symbols[0]
                    var symbolStr = ""
                    if (mSymbol.ph_en?.isNotBlank() == true) {
                        symbolStr += "英[" + mSymbol.ph_en + "]"
                    }
                    if (mSymbol.ph_am?.isNotBlank() == true) {
                        symbolStr += " 美[" + mSymbol.ph_am + "]"
                    }
                    if (symbolStr.isNotBlank()) {
                        sb.append(symbolStr)
                        sb.append("\n")
                    }
                    if (mSymbol.parts?.isNotEmpty() == true) {
                        for (part in mSymbol.parts) {
                            var partStr = ""
                            if (part.part?.isNotBlank() == true) {
                                if (StringUtils.isAllEnglish(part.part)) {
                                    partStr = part.part + " "
                                }
                            }
                            if (part.means?.isNotEmpty() == true) {
                                var meanStr = ""
                                for (mean in part.means) {
                                    meanStr += "$mean；"
                                }
                                if (meanStr.isNotBlank()) {
                                    meanStr = meanStr.substring(0, meanStr.length - 1)
                                    partStr += meanStr
                                }
                            }
                            if (partStr.isNotBlank()) {
                                sb.append(partStr)
                                sb.append("\n")
                            }
                        }
                    }
                    if (mBdJsData.message.baesInfo.exchange != null) {
                        var exresult = ""
                        if (mBdJsData.message.baesInfo.exchange.word_pl?.isNotEmpty() == true) {
                            exresult += "复数:" + mBdJsData.message.baesInfo.exchange.word_pl[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_third?.isNotEmpty() == true) {
                            exresult += "  第三人称单数:" + mBdJsData.message.baesInfo.exchange.word_third[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_past?.isNotEmpty() == true) {
                            exresult += "  过去式:" + mBdJsData.message.baesInfo.exchange.word_past[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_ing?.isNotEmpty() == true) {
                            exresult += "  现在分词:" + mBdJsData.message.baesInfo.exchange.word_ing[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_done?.isNotEmpty() == true) {
                            exresult += "  过去分词:" + mBdJsData.message.baesInfo.exchange.word_done[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_er?.isNotEmpty() == true) {
                            exresult += "  比较级:" + mBdJsData.message.baesInfo.exchange.word_er[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_est?.isNotEmpty() == true) {
                            exresult += "  最高级:" + mBdJsData.message.baesInfo.exchange.word_est[0]
                        }
                        if (mBdJsData.message.baesInfo.exchange.word_noun?.isNotEmpty() == true) {
                            exresult += "  名词:" + mBdJsData.message.baesInfo.exchange.word_noun[0]
                        }
                        if (exresult.isNotBlank()) {
                            sb.append(exresult.trim())
                            sb.append("\n")
                        }
                    }
                    var resultStr = sb.toString().trim()
                    if (resultStr.isNotEmpty()) {
                        result = Record(resultStr, Setings.q)
                    }
                }
            } else if (mBdJsData.message?.baesInfo?.translate_result?.isNotBlank() == true) {
                result = Record(mBdJsData.message.baesInfo.translate_result, Setings.q)
            }
        }
        LogUtil.Log("fromJscbJsToRecord:$result")
        return result
    }

    @Throws(Exception::class)
    private fun fromJscbApiToRecord(mBdJsData: JscbApiData?): Record? {
        var result: Record? = null
        val sb = StringBuilder()
        if (mBdJsData != null && mBdJsData.errno == 0) {
            if (mBdJsData.baesInfo.translate_type == 1) {
                if (mBdJsData.baesInfo.symbols?.isNotEmpty() == true) {
                    val mSymbol = mBdJsData.baesInfo.symbols[0]
                    var symbolStr = ""
                    if (mSymbol.ph_en?.isNotBlank() == true) {
                        symbolStr += "英[" + mSymbol.ph_en + "]"
                    }
                    if (mSymbol.ph_am?.isNotBlank() == true) {
                        symbolStr += " 美[" + mSymbol.ph_am + "]"
                    }
                    if (symbolStr.isNotBlank()) {
                        sb.append(symbolStr)
                        sb.append("\n")
                    }
                    if (mSymbol.parts?.isNotEmpty() == true) {
                        for (part in mSymbol.parts) {
                            var partStr = ""
                            if (part.part?.isNotBlank() == true) {
                                if (StringUtils.isAllEnglish(part.part)) {
                                    partStr = part.part + " "
                                }
                            }
                            if (part.means?.isNotEmpty() == true) {
                                var meanStr = ""
                                for (mean in part.means) {
                                    meanStr += "$mean；"
                                }
                                if (meanStr.isNotBlank()) {
                                    meanStr = meanStr.substring(0, meanStr.length - 1)
                                    partStr += meanStr
                                }
                            }
                            if (partStr.isNotBlank()) {
                                sb.append(partStr)
                                sb.append("\n")
                            }
                        }
                    }
                    if (mBdJsData.baesInfo.exchange != null) {
                        var exresult = ""
                        if (mBdJsData.baesInfo.exchange.word_pl?.isNotEmpty() == true) {
                            exresult += "复数:" + mBdJsData.baesInfo.exchange.word_pl[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_third?.isNotEmpty() == true) {
                            exresult += "  第三人称单数:" + mBdJsData.baesInfo.exchange.word_third[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_past?.isNotEmpty() == true) {
                            exresult += "  过去式:" + mBdJsData.baesInfo.exchange.word_past[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_ing?.isNotEmpty() == true) {
                            exresult += "  现在分词:" + mBdJsData.baesInfo.exchange.word_ing[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_done?.isNotEmpty() == true) {
                            exresult += "  过去分词:" + mBdJsData.baesInfo.exchange.word_done[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_er?.isNotEmpty() == true) {
                            exresult += "  比较级:" + mBdJsData.baesInfo.exchange.word_er[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_est?.isNotEmpty() == true) {
                            exresult += "  最高级:" + mBdJsData.baesInfo.exchange.word_est[0]
                        }
                        if (mBdJsData.baesInfo.exchange.word_noun?.isNotEmpty() == true) {
                            exresult += "  名词:" + mBdJsData.baesInfo.exchange.word_noun[0]
                        }
                        if (exresult.isNotBlank()) {
                            sb.append(exresult.trim())
                            sb.append("\n")
                        }
                    }
                    var resultStr = sb.toString().trim()
                    if (resultStr.isNotEmpty()) {
                        result = Record(resultStr, Setings.q)
                    }
                }
            } else if (mBdJsData.baesInfo.translate_result?.isNotBlank() == true) {
                result = Record(mBdJsData.baesInfo.translate_result, Setings.q)
            }
        }
        LogUtil.Log("fromJscbJsToRecord:$result")
        return result
    }

    private fun getParseYoudaoWebHtmlNew(html: String): Record {
        val sb = StringBuilder()
        val sb_play = StringBuilder()
        var mrecord: Record? = null
        val doc = Jsoup.parse(html)
        val transContent = doc.select("p.trans-content").first()
        if (transContent != null) {
            val text = transContent.text().trim()
            TranslateUtil.addContentAll(text, sb, sb_play)
        }
        val symblos = doc.select("div.per-phone")
        var symbloStr = ""
        for (item in symblos) {
            if (item.childrenSize() > 1) {
                var phoneType = item.select("span").first()?.text()
                val phonetic = item.select("span.phonetic").first()?.text()
                phoneType += phonetic
                symbloStr += "$phoneType "
            }
        }
        if (symbloStr.length > 3) {
            TranslateUtil.addContentAll(symbloStr, sb, sb_play)
        }
        val wordExp = doc.select("li.word-exp")
        for (item in wordExp) {
            val pos = item.select("span.pos").first()?.text()
            var trans = item.select("span.trans").first()?.text()
            if (!pos.isNullOrBlank()) {
                trans = "$pos $trans"
            }
            TranslateUtil.addContentAll(trans, sb, sb_play)
        }
        val zhWordExps = doc.select("li.word-exp-ce.mcols-layout")
        for (item in zhWordExps) {
            val transCE = item.select("div.trans-ce").first()?.text()
            val transCE_ZH = item.select("div.word-exp_tran.grey").first()
            if (!transCE.isNullOrBlank()) {
                val tresult = if (transCE_ZH != null) {
                    transCE + "\n" + transCE_ZH.text()
                } else {
                    transCE
                }
                TranslateUtil.addContentAll(tresult, sb, sb_play)
            }
        }
        val wordWfsLess = doc.select("li.word-wfs-cell-less")
        var wordWfsLessResult = ""
        for (item in wordWfsLess) {
            val wfsName = item.select("span.wfs-name").first()?.text()
            val transformation = item.select("span.transformation").first()?.text()
            val tresult = "$wfsName $transformation"
            wordWfsLessResult += "$tresult  "
        }
        if (wordWfsLessResult.trim().length > 3) {
            wordWfsLessResult = "[${wordWfsLessResult.trim()}]"
            TranslateUtil.addContentAll(wordWfsLessResult, sb, sb_play)
        }
        val exam_type = doc.select("div.exam_type").first()?.text()
        if (!exam_type.isNullOrBlank()) {
            TranslateUtil.addContentAll(exam_type, sb, sb_play)
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getParseYoudaoWebHtml(html: String): Record? {
        val sb = StringBuilder()
        val sb_play = StringBuilder()
        var mrecord: Record? = null
        val doc = Jsoup.parse(html)
        val feedback = doc.select("div.feedback").first()
        if (feedback != null) {
//            LogUtil.DefalutLog(feedback.text());
            return null
        }
        val symblo = doc.select("h2.wordbook-js > div.baav").first()
        if (symblo != null) {
            var text = symblo.text().trim()
            if (text.isNotEmpty() && text.contains("[")) {
                TranslateUtil.addContent(symblo, sb)
            }
        }
        val translate = doc.select("div#phrsListTab > div.trans-container").first()
        if (translate != null) {
            val lis = translate.getElementsByTag("ul").first()
            if (lis != null) {
                for (li in lis.children()) {
                    TranslateUtil.addContentAll(li, sb, sb_play)
                }
            }
            val p = translate.select("p.additional").first()
            if (p != null) {
                TranslateUtil.addContentAll(p, sb, sb_play)
            }
        }
        val fanyiToggle = doc.select("div#fanyiToggle").first()
        if (fanyiToggle != null) {
            val lis = fanyiToggle.getElementsByTag("p")
            if (lis != null && lis.size > 1) {
                TranslateUtil.addContentAll(lis[1], sb, sb_play)
            }
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getParseBingyingWebHtml(html: String): Record {
        LogUtil.Log("---getParseBingyingWebHtml---")
        var mrecord: Record? = null
        val sb = java.lang.StringBuilder()
        val sb_play = java.lang.StringBuilder()
        val doc = Jsoup.parse(html)
        val smt_hw = doc.select("div.smt_hw").first()
        if (smt_hw != null) {
            val p1_11 = doc.select("div.p1-11").first()
            if (p1_11 != null) {
                TranslateUtil.addContentAll(p1_11, sb, sb_play)
            }
        }
        val symblo = doc.select("div.hd_p1_1").first()
        if (symblo != null) {
            TranslateUtil.addContent(symblo, sb)
        }
        val translates = doc.select("div.qdef > ul > li")
        if (translates != null && translates.size > 0) {
            for (li in translates) {
                var content = li.text().trim()
                if (content.contains("网络")) {
                    content = content.replace("网络", "网络：")
                }
                sb.append(content)
                sb.append("\n")
                sb_play.append(content)
                sb_play.append(",")
            }
        }
        val fusu = doc.select("div.qdef > div.hd_div1 > div.hd_if").first()
        if (fusu != null) {
            TranslateUtil.addContentAll(fusu, sb, sb_play)
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getParseHjiangWebHtml(html: String): Record? {
        val sb = StringBuilder()
        val sb_play = StringBuilder()
        var mrecord: Record? = null
        val doc = Jsoup.parse(html)
        val error = doc.select("div.word-notfound").first()
        if (error != null) {
            LogUtil.Log(error.text())
            return null
        }
        val symblo = doc.select("div.word-info > div.pronounces").first()
        if (symblo != null) {
            TranslateUtil.addContent(symblo, sb)
        }
        val translate = doc.select("div.simple").first()
        if (translate != null) {
            for (li in translate.children()) {
                TranslateUtil.addContentAll(li, sb, sb_play)
            }
        }
        val resutlStr = sb.toString().trim()
        mrecord = Record(resutlStr, Setings.q)
        mrecord.backup1 = sb_play.toString()
        return mrecord
    }

    private fun getSortData(map: Map<String, String>?): String {
        var result = ""
        if (map != null) {
            for ((key, value) in map) {
                result += "$key=$value&"
            }
            result += "app_key=" + Setings.QQAPPKEY
            result = MD5.encode(result).uppercase(Locale.getDefault())
        }
        return result
    }

    fun setBdJsApiKeys(jsonStr: String) {
        try {
            val mBdJsKeyData = Gson().fromJson(jsonStr, BdJsKeyData::class.java)
            if (mBdJsKeyData != null) {
                Setings.BdJsCookie = mBdJsKeyData.BdJsCookie
                Setings.BdJsOrigin = mBdJsKeyData.BdJsOrigin
                Setings.BdJsReferer = mBdJsKeyData.BdJsReferer
                Setings.BdJsSimpleMeansFlag = mBdJsKeyData.BdJsSimpleMeansFlag
                Setings.BdJsToken = mBdJsKeyData.BdJsToken
                Setings.BdJsTranstype = mBdJsKeyData.BdJsTranstype
                Setings.BdJsdomain = mBdJsKeyData.BdJsdomain
                Setings.BdJsSign1 = mBdJsKeyData.BdJsSign1
                Setings.BdJsSign2 = mBdJsKeyData.BdJsSign2
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //examination task
    suspend fun examFromJscbJs(): JscbJsData? {
        LogUtil.Log("examFromJscbJs")
        var result: JscbJsData? = null
        try {
            val service = RetrofitBuilder.getKTranRetrofitService(Setings.JscbJsTranBaseUrl)
            val timestamp: String = System.currentTimeMillis().toString()
            val q = URLEncoder.encode(Setings.q, "utf-8")
            val params = "61000006$timestamp$q"
            val sign = KSettings.getJscbJsSign(params)
            val mResponseBody = service.tranByJscbJs(q, sign, timestamp)
            val responseStr = mResponseBody.body()?.string()
            if (!TextUtils.isEmpty(responseStr)) {
                result = GsonBuilder()
                    .registerTypeHierarchyAdapter(List::class.java, ArraySecurityAdapter())
                    .create()
                    .fromJson(responseStr, JscbJsData::class.java)
                LogUtil.Log("Result---examFromJscbJs:$result")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }
}