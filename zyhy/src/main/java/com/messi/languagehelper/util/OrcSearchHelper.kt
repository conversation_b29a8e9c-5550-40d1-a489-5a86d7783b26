package com.messi.languagehelper.util

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.core.content.FileProvider
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.R
import com.messi.languagehelper.bean.YoudaoPhotoBean
import com.messi.languagehelper.dialog.OCRDialog
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.http.UICallback
import com.messi.languagehelper.impl.FragmentProgressbarListener
import com.messi.languagehelper.util.ToastUtil.diaplayMesShort
import com.mzxbkj.baselibrary.util.LogUtil
import com.squareup.moshi.Moshi
import okhttp3.FormBody
import java.io.File

/**
 * Created by luli on 06/07/2017.
 */
class OrcSearchHelper(private val context: Activity) {

    private var orc_api_retry_times = 2
    private var mCurrentPhotoPath: String? = null
    private val mProgressbarListener: FragmentProgressbarListener? = null

    fun photoSelectDialog() {
        orc_api_retry_times = 2
        val titles = arrayOf(
            context.resources.getString(R.string.take_photo),
            context.resources.getString(R.string.photo_album)
        )
        val mPhonoSelectDialog = OCRDialog(context, titles, true)
        mPhonoSelectDialog.setListener(object : OCRDialog.PopViewItemOnclickListener {
            override fun onSecondClick() {
                imageFromAlbum
            }

            override fun onFirstClick() {
                imageFromCamera
            }
        })
        mPhonoSelectDialog.show()
    }

    //相片类型
    val imageFromAlbum: Unit
        get() {
            try {
                val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                intent.type = "image/*" //相片类型
                context.startActivityForResult(intent, CameraUtil.REQUEST_CODE_PICK_IMAGE)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    val imageFromCamera: Unit
        get() {
            try {
                LogUtil.Log("has permission")
                startCamera()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    private fun startCamera() {
        val state = Environment.getExternalStorageState()
        if (state == Environment.MEDIA_MOUNTED) {
            val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            if (takePictureIntent.resolveActivity(context.packageManager) != null) {
                val photoFile = CameraUtil.createImageFile()
                mCurrentPhotoPath = photoFile.absolutePath
                LogUtil.Log("img uri:$mCurrentPhotoPath")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    takePictureIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    val imageUri = FileProvider.getUriForFile(
                        context, Setings.getProvider(context), photoFile
                    )
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri)
                } else {
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(photoFile))
                }
                context.startActivityForResult(
                    takePictureIntent,
                    CameraUtil.REQUEST_CODE_CAPTURE_CAMEIA
                )
            } else {
                diaplayMesShort(context, "请确认已经插入SD卡")
            }
        }
    }

    fun doCropPhoto(uri: Uri?) {
        val photoTemp = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
            File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),"crop_image.jpg")
        } else {
            File(CameraUtil.createTempFile())
        }
        val mOutUri = Uri.fromFile(photoTemp)
        val intent = Intent("com.android.camera.action.CROP")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        intent.setDataAndType(uri, "image/*")
        intent.putExtra("crop", "true")
        intent.putExtra("scale", true)
        intent.putExtra("return-data", false)
        intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString())
        intent.putExtra(MediaStore.EXTRA_OUTPUT, mOutUri)
        intent.putExtra("noFaceDetection", false)
        context.startActivityForResult(intent, CameraUtil.PHOTO_PICKED_WITH_DATA)
    }

    fun sendYoudaoOCR() {
        try {
            loadding()
            val path = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
                File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),"crop_image.jpg").absolutePath
            } else {
                CameraUtil.createTempFile()
            }
            val base64 =
                "data:image/png;base64," + CameraUtil.getImageBase64(path, 600f, 600f, 1024)
            LogUtil.Log("YoudaoOCR-base64:$base64")
            val formBody: FormBody = FormBody.Builder()
                .add("imgBase", base64)
                .build()
            LanguagehelperHttpClient.postWithHeader(
                Setings.YDOcrQuestion,
                formBody,
                object : UICallback(
                    context
                ) {
                    override fun onResponsed(responseString: String) {
                        if (JsonParser.isJson(responseString)) {
                            LogUtil.Log("YoudaoOCR:$responseString")
                            val moshi = Moshi.Builder().build()
                            val jsonAdapter = moshi.adapter(YoudaoPhotoBean::class.java)
                            val bean = jsonAdapter.fromJson(responseString)
                            LiveEventBus.get(KeyUtil.YoudaoPhotoBean, YoudaoPhotoBean::class.java)
                                .post(bean)
                        } else {
                            showToast(context.resources.getString(R.string.server_error))
                        }
                    }

                    override fun onFailured() {
                        showToast(context.resources.getString(R.string.network_error))
                    }

                    override fun onFinished() {
                        finishLoadding()
                    }
                })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == CameraUtil.REQUEST_CODE_PICK_IMAGE && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                val uri = data.data
                uri?.let { doCropPhoto(it) }
            }
        } else if (requestCode == CameraUtil.REQUEST_CODE_CAPTURE_CAMEIA && resultCode == Activity.RESULT_OK) {
            var contentUri: Uri? = null
            contentUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                FileProvider.getUriForFile(
                    context, Setings.getProvider(
                        context
                    ), File(mCurrentPhotoPath)
                )
            } else {
                Uri.fromFile(File(mCurrentPhotoPath))
            }
            doCropPhoto(contentUri)
        } else if (requestCode == CameraUtil.PHOTO_PICKED_WITH_DATA && resultCode == Activity.RESULT_OK) {
            sendYoudaoOCR()
        }
    }

    private fun loadding() {
        mProgressbarListener?.showProgressbar()
    }

    /**
     * 通过接口回调activity执行进度条显示控制
     */
    private fun finishLoadding() {
        mProgressbarListener?.hideProgressbar()
    }

    private fun showToast(toastString: String) {
        diaplayMesShort(context, toastString)
    }
}