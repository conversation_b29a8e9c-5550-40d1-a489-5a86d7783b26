package com.messi.languagehelper.util

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import com.canhub.cropper.CropImage
import com.canhub.cropper.CropImageActivity
import com.canhub.cropper.CropImageOptions
import com.messi.languagehelper.R
import com.messi.languagehelper.bean.BaiduOcrRoot
import com.messi.languagehelper.dialog.OCRDialog
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.http.UICallback
import com.messi.languagehelper.impl.OrcResultListener
import com.messi.languagehelper.util.ToastUtil.diaplayMesShort
import com.mzxbkj.baselibrary.util.LogUtil
import com.squareup.moshi.Moshi
import java.io.File

/**
 * Created by luli on 06/07/2017.
 */
class OrcHelper(
    val fragment: Fragment,
    val mOrcResultListener: OrcResultListener?,
) {
    private var orc_api_retry_times = 2
    private val mActivity: Activity? = fragment.activity
    private var mCurrentPhotoPath: String? = null

    fun photoSelectDialog() {
        if (!mActivity!!.isFinishing) {
            orc_api_retry_times = 2
            val titles = arrayOf(
                mActivity.resources.getString(R.string.take_photo),
                mActivity.resources.getString(R.string.photo_album)
            )
            val mPhonoSelectDialog = OCRDialog(mActivity, titles)
            mPhonoSelectDialog.setListener(object : OCRDialog.PopViewItemOnclickListener {
                override fun onSecondClick() {
                    imageFromAlbum()
                }

                override fun onFirstClick() {
                    imageFromCamera()
                }
            })
            mPhonoSelectDialog.show()
        }
    }

    //相片类型
    fun imageFromAlbum() {
        try {
            LogUtil.Log("imageFromAlbum")
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*") //相片类型
            fragment.startActivityForResult(intent, CameraUtil.REQUEST_CODE_PICK_IMAGE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun imageFromCamera() {
        try {
            startCamera()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun startCamera() {
        LogUtil.Log("startCamera")
        val state = Environment.getExternalStorageState()
        if (state == Environment.MEDIA_MOUNTED) {
            val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            if (takePictureIntent.resolveActivity(mActivity!!.packageManager) != null) {
                val photoFile = CameraUtil.createImageFile()
                mCurrentPhotoPath = photoFile.absolutePath
                LogUtil.Log("img uri:$mCurrentPhotoPath")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    takePictureIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    val imageUri = FileProvider.getUriForFile(
                        mActivity, Setings.getProvider(mActivity), photoFile
                    )
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri)
                } else {
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(photoFile))
                }
                fragment.startActivityForResult(
                    takePictureIntent,
                    CameraUtil.REQUEST_CODE_CAPTURE_CAMEIA
                )
            } else {
                diaplayMesShort(mActivity, "请确认已经插入SD卡")
            }
        }
    }


    private fun doCropPhoto(uri: Uri) {
        val destinationUri = Uri.fromFile(File(fragment.requireContext().cacheDir, "cropped"))
        val options = CropImageOptions(
            imageSourceIncludeGallery = false,
            imageSourceIncludeCamera = false,
            customOutputUri = destinationUri,
            outputCompressQuality = 70,
            initialCropWindowPaddingRatio = 0.02f,
        )
        val bundle = Bundle()
        bundle.putParcelable(CropImage.CROP_IMAGE_EXTRA_SOURCE, uri)
        bundle.putParcelable(CropImage.CROP_IMAGE_EXTRA_OPTIONS, options)
        val intent = Intent(mActivity, CropImageActivity::class.java)
        intent.putExtra(CropImage.CROP_IMAGE_EXTRA_BUNDLE, bundle)
        fragment.startActivityForResult(intent, CameraUtil.PHOTO_PICKED_WITH_DATA)
    }

    fun sendBaiduOCR() {
        try {
            LogUtil.Log("sendBaiduOCR")
            loadding()
            val photoPath = File(fragment.requireContext().cacheDir, "cropped").absolutePath
            LogUtil.Log("sendBaiduOCR:$photoPath")
            LanguagehelperHttpClient.postBaiduOCR(
                mActivity,
                photoPath,
                object : UICallback(mActivity) {
                    override fun onResponsed(responseString: String?) {
                        if (JsonParser.isJson(responseString)) {
                            val moshi = Moshi.Builder().build()
                            val adapter = moshi.adapter(BaiduOcrRoot::class.java)
                            val mBaiduOcrRoot = adapter.fromJson(responseString)
                            if (mBaiduOcrRoot != null) {
                                if (mBaiduOcrRoot.words_result_num > 0) {
                                    mOrcResultListener?.ShowResult(mBaiduOcrRoot)
                                } else {
                                    if (mBaiduOcrRoot.error_code in 91..119) {
                                        Setings.saveSharedPreferences(
                                            KSettings.getSP(mActivity!!),
                                            KeyUtil.BaiduAccessToken,
                                            ""
                                        )
                                        Setings.saveSharedPreferences(
                                            KSettings.getSP(mActivity),
                                            KeyUtil.BaiduAccessTokenExpires,
                                            0.toLong()
                                        )
                                        Setings.saveSharedPreferences(
                                            KSettings.getSP(mActivity),
                                            KeyUtil.BaiduAccessTokenCreateAt,
                                            0.toLong()
                                        )
                                        if (orc_api_retry_times > 0) {
                                            orc_api_retry_times--
                                            sendBaiduOCR()
                                        }
                                    } else {
                                        if (!TextUtils.isEmpty(mBaiduOcrRoot.error_msg)) {
                                            showToast(mBaiduOcrRoot.error_msg)
                                        }
                                    }
                                }
                            }
                        } else {
                            showToast(mActivity!!.resources.getString(R.string.server_error))
                        }
                    }

                    override fun onFailured() {
                        showToast(mActivity!!.resources.getString(R.string.network_error))
                    }

                    override fun onFinished() {
                        finishLoadding()
                    }
                })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        try {
            LogUtil.Log("requestCode:$requestCode---resultCode:$resultCode")
            if (resultCode == Activity.RESULT_OK) {
                if (requestCode == CameraUtil.REQUEST_CODE_PICK_IMAGE) {
                    if (data != null) {
                        val uri = data.data
                        uri?.let { doCropPhoto(it) }
                    }
                } else if (requestCode == CameraUtil.REQUEST_CODE_CAPTURE_CAMEIA) {
                    var contentUri: Uri? = null
                    contentUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        FileProvider.getUriForFile(
                            mActivity!!,
                            Setings.getProvider(mActivity),
                            File(mCurrentPhotoPath)
                        )
                    } else {
                        Uri.fromFile(File(mCurrentPhotoPath))
                    }
                    doCropPhoto(contentUri)
                } else if (requestCode == CameraUtil.PHOTO_PICKED_WITH_DATA && resultCode == Activity.RESULT_OK) {
                    sendBaiduOCR()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun loadding() {
    }

    /**
     * 通过接口回调activity执行进度条显示控制
     */
    private fun finishLoadding() {
    }

    private fun showToast(toastString: String?) {
        diaplayMesShort(mActivity, toastString)
    }
}