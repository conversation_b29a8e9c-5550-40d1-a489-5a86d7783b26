package com.messi.languagehelper

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.leancloud.LCObject
import cn.leancloud.LCQuery
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.adapter.RcReadingListAdapter
import com.messi.languagehelper.bean.BoutiquesBean
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.Reading
import com.messi.languagehelper.databinding.ReadingActivityBinding
import com.messi.languagehelper.httpservice.RetrofitBuilder
import com.messi.languagehelper.service.PlayerService
import com.messi.languagehelper.util.AVOUtil
import com.messi.languagehelper.util.DataUtil
import com.messi.languagehelper.util.KeyUtil
import com.messi.languagehelper.util.Setings
import com.messi.languagehelper.util.ToastUtil
import com.mzxbkj.baselibrary.util.BaseSetings
import com.mzxbkj.baselibrary.util.LogUtil
import com.mzxbkj.baselibrary.util.SignUtil
import com.squareup.moshi.Moshi
import com.yqritc.recyclerviewflexibledivider.HorizontalDividerItemDecoration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID

open class ReadingsActivity : BaseActivity() {

    private lateinit var mAdapter: RcReadingListAdapter
    private lateinit var avObjects: ArrayList<Reading>
    private lateinit var mLinearLayoutManager: LinearLayoutManager
    private lateinit var binding: ReadingActivityBinding
    private var skip = 0
    private var category: String? = null
    private var quest: String? = null
    private var type: String? = null
    private var source: String? = null
    private var boutique_code: String? = null
    private var isLoading: Boolean = false
    private var noMoreData: Boolean = false
    private var mBoutiquesBean: BoutiquesBean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        changeStatusBarTextColor(true)
        setStatusbarColor(R.color.white)
        binding = ReadingActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        setToolbarStyleWhile()
        registerBroadcast()
        initViews()
        queryTask()
        initCollectedButton()
    }

    private fun initViews() {
        val bundle = intent.getBundleExtra(KeyUtil.BundleKey)
        bundle?.let {
            category = bundle.getString(KeyUtil.Category)
            quest = bundle.getString(KeyUtil.SearchKey)
            type = bundle.getString(KeyUtil.NewsType)
            source = bundle.getString(KeyUtil.NewsSource)
            boutique_code = bundle.getString(KeyUtil.BoutiqueCode)
            mBoutiquesBean = bundle.getParcelable(KeyUtil.ObjectKey)
        }
        LogUtil.Log("category:$category,quest:$quest,type:$type,source:$source")
        setListOnScrollListener()
        avObjects = ArrayList()
        initSwipeRefresh()
        mAdapter = RcReadingListAdapter(this, avObjects)
        mAdapter.setItems(avObjects)
        mAdapter.footer = Any()
        hideFooterview()
        mLinearLayoutManager = LinearLayoutManager(this)
        binding.listview.layoutManager = mLinearLayoutManager
        binding.listview.addItemDecoration(
            HorizontalDividerItemDecoration.Builder(this)
                .colorResId(R.color.text_tint)
                .sizeResId(R.dimen.list_divider_size)
                .marginResId(R.dimen.padding_2, R.dimen.padding_2)
                .build()
        )
        binding.listview.adapter = mAdapter
        binding.collectBtn.setOnClickListener { collectedOrUncollected() }

        updateDataBackground(mBoutiquesBean)
    }

    private fun updateDataBackground(mBoutiquesBean: BoutiquesBean?) {
        if (mBoutiquesBean != null && !TextUtils.isEmpty(mBoutiquesBean.objectId)) {
            binding.collectBtn.visibility = View.VISIBLE
            lifecycleScope.launch {
                updateStatus(mBoutiquesBean)
            }
        } else {
            binding.collectBtn.visibility = View.GONE
        }
    }

    private suspend fun updateStatus(mBoutiquesBean: BoutiquesBean) =
        withContext(Dispatchers.IO) {
            try {
                val mBoutiques = LCObject.createWithoutData(
                    AVOUtil.Boutiques.Boutiques,
                    mBoutiquesBean.objectId
                )
                mBoutiques.increment(AVOUtil.Boutiques.views)
                mBoutiques.save()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    fun setListOnScrollListener() {
        binding.listview.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val visible = mLinearLayoutManager.childCount
                val total = mLinearLayoutManager.itemCount
                val firstVisibleItem = mLinearLayoutManager.findFirstCompletelyVisibleItemPosition()
                if (visible + firstVisibleItem >= total) {
                    queryTask()
                }
            }
        })
    }

    private fun initCollectedButton() {
        binding.volumeImg.visibility = View.GONE
        if (mBoutiquesBean != null) {
            if (!TextUtils.isEmpty(mBoutiquesBean!!.code)) {
                binding.volumeImg.visibility = View.VISIBLE
                if (BoxHelper.isCollected(mBoutiquesBean!!.code)) {
                    binding.volumeImg.setImageResource(R.drawable.ic_collected_white)
                    binding.volumeImg.tag = true
                } else {
                    binding.volumeImg.setImageResource(R.drawable.ic_uncollected_white)
                    binding.volumeImg.tag = false
                }
            }
        }
        LogUtil.Log("bTag:" + binding.volumeImg.tag)
    }

    private fun collectedOrUncollected() {
        val tag = !(binding.volumeImg.tag as Boolean)
        binding.volumeImg.tag = tag
        if (mBoutiquesBean != null) {
            if (tag) {
                binding.volumeImg.setImageResource(R.drawable.ic_collected_white)
                ToastUtil.diaplayMesShort(this, "已收藏")
            } else {
                binding.volumeImg.setImageResource(R.drawable.ic_uncollected_white)
                ToastUtil.diaplayMesShort(this, "取消收藏")
            }
            val cdata = CollectedData()
            cdata.objectId = mBoutiquesBean!!.code
            if (tag) {
                cdata.name = mBoutiquesBean!!.title
                cdata.type = AVOUtil.Boutiques.Boutiques
                val moshi = Moshi.Builder().build()
                val jsonAdapter = moshi.adapter(BoutiquesBean::class.java)
                cdata.json = jsonAdapter.toJson(mBoutiquesBean)
                BoxHelper.insert(cdata)
            } else {
                BoxHelper.remove(cdata)
            }
            LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
        }
    }

    override fun updateUI(music_action: String) {
        when (music_action) {
            PlayerService.action_loading -> {
                showProgressbar()
            }

            PlayerService.action_finish_loading -> {
                hideProgressbar()
            }

            else -> {
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun hideFooterview() {
        mAdapter.hideFooter()
    }

    private fun showFooterview() {
        mAdapter.showFooter()
    }

    override fun onSwipeRefreshLayoutRefresh() {
        isLoading = false
        noMoreData = false
        hideFooterview()
        skip = 0
        avObjects.clear()
        mAdapter.notifyDataSetChanged()
        queryTask()
    }

    private fun queryTask() {
        if (isLoading || noMoreData) return
        isLoading = true
        lifecycleScope.launch {
            showProgressbar()
            val results = withContext(Dispatchers.IO) {
                getDataTask()
            }
            onPostExecute(results)
        }
    }

    private fun getDataTask(): List<LCObject>? {
        val query = LCQuery<LCObject>(AVOUtil.Reading.Reading)
        if (!TextUtils.isEmpty(category)) {
            query.whereEqualTo(AVOUtil.Reading.category, category)
        }
        if (!TextUtils.isEmpty(quest)) {
            query.whereContains(AVOUtil.Reading.title, quest)
        }
        if (!TextUtils.isEmpty(type)) {
            query.whereEqualTo(AVOUtil.Reading.type, type)
        }
        if (!TextUtils.isEmpty(source)) {
            query.whereEqualTo(AVOUtil.Reading.source_name, source)
        }
        if (!TextUtils.isEmpty(boutique_code)) {
            query.whereEqualTo(AVOUtil.Reading.boutique_code, boutique_code)
        }
        query.addDescendingOrder(AVOUtil.Reading.publish_time)
        query.skip(skip)
        query.limit(Setings.video_list)
        try {
            return query.find()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    protected fun onPostExecute(avObject: List<LCObject>?) {
        isLoading = false
        hideProgressbar()
        onSwipeRefreshLayoutFinish()
        if (avObject != null) {
            if (avObject.isEmpty()) {
                noMoreData = true
                hideFooterview()
                if (skip == 0 && !TextUtils.isEmpty(category) && category == AVOUtil.Category.composition) {
                    if (!TextUtils.isEmpty(quest)) {
                        showFeedbackDialog()
                    }
                }
            } else {
                if (skip == 0) {
                    avObjects.clear()

                }
                DataUtil.changeDataToReading(avObject, avObjects, false)
//                loadAD()
                mAdapter.notifyDataSetChanged()
                skip += Setings.video_list
                if (avObject.size < Setings.video_list) {
                    noMoreData = true
                    hideFooterview()
                } else {
                    noMoreData = false
                    showFooterview()
                }
            }
        } else {
            ToastUtil.diaplayMesShort(this, "加载失败，下拉可刷新")
        }
    }

    private fun showFeedbackDialog() {
        val builder = AlertDialog.Builder(this, R.style.CustomAlertDialog)
        builder.setTitle(getString(R.string.no_found))
        builder.setMessage(getString(R.string.compisition_request))
        builder.setPositiveButton(getString(R.string.title_sure)) { dialog, _ ->
            submitEssay(quest!!)
            dialog.dismiss()
        }
        builder.setNegativeButton(getString(R.string.cancel)) { dialog, _ ->
            dialog.dismiss()
        }
        val dialog = builder.create()
        dialog.show()
    }

    private fun submitEssay(question: String) {
        lifecycleScope.launch(Dispatchers.Main) {
            delay(1)
            showProgressbar()
            val result = uploadCompisition(question)
            hideProgressbar()
            if (TextUtils.isEmpty(result)) {
                ToastUtil.diaplayMesShort(this@ReadingsActivity, getString(R.string.network_error))
            } else {
                ToastUtil.diaplayMesShort(this@ReadingsActivity, result)
            }

        }
    }

    suspend fun uploadCompisition(question: String): String = withContext(Dispatchers.IO) {
        var result = ""
        try {
            val mParams = UUID.randomUUID().toString()
            val timeStamp = System.currentTimeMillis().toString()
            val sign =
                SignUtil.getMd5Sign(BaseSetings.TSLVK, mParams, timeStamp, BaseSetings.appPacket)
            val responseResult = RetrofitBuilder.xService.createComposition(
                question = question,
                timestamp = timeStamp,
                params = mParams,
                sign = sign
            )
            val mResult = responseResult.body()
            LogUtil.Log(mResult.toString())
            if (mResult != null) {
                result = mResult.error
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        result
    }

    override fun onResume() {
        super.onResume()
        if (::mAdapter.isInitialized) {
            mAdapter.notifyDataSetChanged()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterBroadcast()
    }
}