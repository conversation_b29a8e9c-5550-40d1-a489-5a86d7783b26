package com.messi.languagehelper

import android.content.SharedPreferences
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.box.BoxHelper
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.Reading
import com.messi.languagehelper.databinding.ReadingMp3DetailActivityBinding
import com.messi.languagehelper.service.PlayerService
import com.messi.languagehelper.util.*
import com.messi.languagehelper.util.KSettings.notificationGuide
import com.mzxbkj.baselibrary.util.LogUtil
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ReadingMp3DetailActivity : BaseActivity(), OnSeekBarChangeListener {

    private var mAVObject: Reading? = null
    private var mAVObjects: List<Reading>? = null
    private lateinit var mSharedPreferences: SharedPreferences
    private var index = 0
    private val handler: Handler = Handler(Looper.getMainLooper())
    private lateinit var binding: ReadingMp3DetailActivityBinding

    private val mRunnable: Runnable = object : Runnable {
        override fun run() {
            setSeekbarAndText()
            handler.postDelayed(this, 300)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ReadingMp3DetailActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        registerBroadcast()
        initData()
        setData()
        guide()
    }

    private fun initData() {
        mSharedPreferences = getSharedPreferences(this.packageName, MODE_PRIVATE)
        index = intent.getIntExtra(KeyUtil.IndexKey, 0)
        val data = Setings.dataMap[KeyUtil.DataMapKey]
        Setings.dataMap.clear()
        if (data is List<*>) {
            mAVObjects = data as List<Reading>
            if (mAVObjects != null && mAVObjects!!.size > index) {
                mAVObject = mAVObjects!![index]
            }
        }
    }

    private fun setData() {
        if (mAVObject == null) {
            finish()
            return
        }
        initClicked()
        binding.seekbar.setOnSeekBarChangeListener(this)
        binding.toolbarLayout.title = mAVObject!!.title
        binding.title.text = mAVObject!!.title
        binding.scrollview.scrollTo(0, 0)
        if (!TextUtils.isEmpty(mAVObject!!.img_url)) {
            binding.adImg.visibility = View.VISIBLE
            binding.spaceLayout.visibility = View.GONE
            binding.adImg.setImageURI(mAVObject!!.img_url)
        }
        TextHandlerUtil.handlerText(this, binding.content, mAVObject!!.content)

        if (PlayerUtil.isSameMedia(mAVObject)) {
            if (PlayerUtil.isPlaying()) {
                binding.btnPlay.isSelected = true
                handler.postDelayed(mRunnable, 300)
            }
            setSeekbarAndText()
        }
        if ("text" == mAVObject!!.type) {
            binding.playerLayout.visibility = View.GONE
        }
        if (TextUtils.isEmpty(mAVObject!!.status)) {
            mAVObject!!.status = "1"
            BoxHelper.updateState(mAVObject)
        }
        if (BoxHelper.isCollected(mAVObject!!.object_id)) {
            mAVObject!!.isCollected = "1"
        } else {
            mAVObject!!.isCollected = ""
        }
    }

    private fun setSeekbarAndText() {
        if (PlayerUtil.isSameMedia(mAVObject)) {
            val currentPosition = PlayerUtil.currentPosition
            val mDuration = PlayerUtil.duration
            if (mDuration > 0) {
                binding.seekbar.max = mDuration
                binding.timeDuration.text = TimeUtil.getDuration(mDuration / 1000)
            }
            binding.seekbar.progress = currentPosition
            binding.timeCurrent.text = TimeUtil.getDuration(currentPosition / 1000)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.composition, menu)
        return true
    }

    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        if (menu.size() > 1) {
            setMenuIcon(menu.getItem(1))
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        super.onOptionsItemSelected(item)
        when (item.itemId) {
            R.id.action_share -> copyOrshare(0)
            R.id.action_collected -> {
                setCollectStatus()
                setMenuIcon(item)
                updateData()
            }
        }
        return true
    }

    private fun setCollectStatus() {
        if (TextUtils.isEmpty(mAVObject!!.isCollected)) {
            mAVObject!!.isCollected = "1"
        } else {
            mAVObject!!.isCollected = ""
        }
    }

    private fun updateData() {
        lifecycleScope.launch(Dispatchers.IO) {
            if (mAVObject != null) {
                if (!TextUtils.isEmpty(mAVObject!!.isCollected)) {
                    val cdata = CollectedData()
                    cdata.objectId = mAVObject!!.object_id
                    cdata.name = mAVObject!!.title
                    cdata.type = AVOUtil.Reading.Reading
                    val moshi = Moshi.Builder().build()
                    val jsonAdapter = moshi.adapter(Reading::class.java)
                    cdata.json = jsonAdapter.toJson(mAVObject)
                    BoxHelper.insert(cdata)
                } else {
                    val cdata = CollectedData()
                    cdata.objectId = mAVObject!!.object_id
                    BoxHelper.remove(cdata)
                }
                LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
            }
        }
    }

    private fun setMenuIcon(item: MenuItem) {
        if (TextUtils.isEmpty(mAVObject!!.isCollected)) {
            item.icon = getDrawable(R.drawable.ic_uncollected_white)
        } else {
            item.icon = getDrawable(R.drawable.ic_collected_white)
        }
    }

    private fun copyOrshare(i: Int) {
        val sb = StringBuilder()
        sb.append(mAVObject!!.title)
        sb.append("\n")
        sb.append(mAVObject!!.content)
        if (i == 0) {
            Setings.share(this, sb.toString())
        } else {
            Setings.copy(this, sb.toString())
        }
    }

    private fun initClicked() {
        binding.playbtnLayout.setOnClickListener {
            notificationGuide(this)
            PlayerUtil.initAndPlay(mAVObject)
        }
    }

    override fun updateUI(music_action: String) {
        LogUtil.Log("updateUI:$music_action")
        if (PlayerUtil.isSameMedia(mAVObject)) {
            if (PlayerService.action_restart == music_action) {
                binding.btnPlay.isSelected = false
                handler.removeCallbacks(mRunnable)
            } else if (PlayerService.action_pause == music_action) {
                binding.btnPlay.isSelected = true
                handler.postDelayed(mRunnable, 300)
            }
        }
        if (PlayerService.action_loading == music_action) {
            showProgressbar()
        } else if (PlayerService.action_finish_loading == music_action) {
            hideProgressbar()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterBroadcast()
        handler.removeCallbacks(mRunnable)
    }

    private fun guide() {
        if (!mSharedPreferences.getBoolean(KeyUtil.isReadingDetailGuideShow, false)) {
            val builder = AlertDialog.Builder(this, R.style.Theme_AppCompat_Light_Dialog_Alert)
            builder.setTitle("")
            builder.setMessage("点击英文单词即可查询词意。")
            builder.setPositiveButton("确认", null)
            val dialog = builder.create()
            dialog.show()
            Setings.saveSharedPreferences(
                mSharedPreferences,
                KeyUtil.isReadingDetailGuideShow,
                true
            )
        }
    }

    override fun onProgressChanged(seekBar: SeekBar, i: Int, b: Boolean) {}
    override fun onStartTrackingTouch(seekBar: SeekBar) {
        handler.removeCallbacks(mRunnable)
        PlayerUtil.pause()
    }

    override fun onStopTrackingTouch(seekBar: SeekBar) {
        PlayerUtil.seekTo(seekBar.progress)
        PlayerUtil.play()
        handler.postDelayed(mRunnable, 300)
    }
}