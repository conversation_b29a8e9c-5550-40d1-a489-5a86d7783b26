package com.messi.languagehelper

import android.content.SharedPreferences
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.*
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.box.BoxHelper.insert
import com.messi.languagehelper.box.BoxHelper.isCollected
import com.messi.languagehelper.box.BoxHelper.remove
import com.messi.languagehelper.box.BoxHelper.updateState
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.Reading
import com.messi.languagehelper.databinding.ReadingMp3LrcDetailActivityBinding
import com.messi.languagehelper.http.BgCallback
import com.messi.languagehelper.http.LanguagehelperHttpClient
import com.messi.languagehelper.service.PlayerService
import com.messi.languagehelper.util.*
import com.mzxbkj.baselibrary.util.LogUtil
import com.squareup.moshi.Moshi

class ReadingDetailLrcActivity : BaseActivity(), SeekBar.OnSeekBarChangeListener {

    private var mAVObject: Reading? = null
    private var mAVObjects: List<Reading>? = null
    private var list: List<utilLrc.Statement>? = null
    private var currentIndex = 0
    private var currentItem: utilLrc.Statement? = null
    private var nextTopTime = 0.0
    private var handler: Handler? = Handler(Looper.getMainLooper())
    private lateinit var sb: StringBuilder
    private lateinit var mSharedPreferences: SharedPreferences
    private lateinit var binding: ReadingMp3LrcDetailActivityBinding

    private val mRunnable: Runnable = object : Runnable {
        override fun run() {
            setSeekbarAndText()
            handler!!.postDelayed(this, 50)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ReadingMp3LrcDetailActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        registerBroadcast()
        initData()
        downloadLrc()
        setData()
        guide()
    }

    private fun initData() {
        mSharedPreferences = KSettings.getSP(this)
        sb = StringBuilder()
        val index = intent.getIntExtra(KeyUtil.IndexKey, 0)
        val data = Setings.dataMap[KeyUtil.DataMapKey]
        Setings.dataMap.clear()
        if (data is List<*>) {
            mAVObjects = data as List<Reading>?
            if (mAVObjects != null && mAVObjects!!.size > index) {
                mAVObject = mAVObjects!![index]
            }
        }
        if (mAVObject == null) {
            finish()
            return
        }
    }

    private fun setData() {
        setActionBarTitle(" ")
        binding.seekbar.setOnSeekBarChangeListener(this)
        if (!TextUtils.isEmpty(mAVObject!!.title)) {
            binding.title.text = mAVObject!!.title
        }
        binding.playbtnLayout.setOnClickListener { onPlayBtnClick() }
        binding.recognizeKnow.setOnClickListener { playNext() }
        binding.recognizeUnknow.setOnClickListener { addUnknow() }
        binding.playPrevious.setOnClickListener { playPrevious() }
        binding.subtitle.setOnClickListener {
            if (binding.content.isShown) {
                binding.content.visibility = View.GONE
            } else {
                binding.content.visibility = View.VISIBLE
            }
        }
        if (PlayerUtil.isSameMedia(mAVObject!!)) {
            if (PlayerUtil.isPlaying()) {
                binding.btnPlay.isSelected = true
                handler!!.postDelayed(mRunnable, 50)
                downloadLrc()
            }
            setSeekbarAndText()
        }
        if (TextUtils.isEmpty(mAVObject!!.status)) {
            mAVObject!!.status = "1"
            updateState(mAVObject)
        }
        if (isCollected(mAVObject!!.object_id)) {
            mAVObject!!.isCollected = "1"
        } else {
            mAVObject!!.isCollected = ""
        }
        if (mAVObject!!.duration > 0) {
            binding.timeDuration.text = TimeUtil.getDuration(mAVObject!!.duration)
        }
    }

    private fun setSeekbarAndText() {
        if (PlayerUtil.isSameMedia(mAVObject)) {
            val currentPosition = PlayerUtil.currentPosition
            val mDuration = PlayerUtil.duration
            binding.seekbar.max = mDuration
            if (mAVObject!!.duration == 0 && mDuration > 0) {
                binding.timeDuration.text = TimeUtil.getDuration(mDuration / 1000)
            }
            binding.seekbar.progress = currentPosition
            binding.timeCurrent.text = TimeUtil.getDuration(currentPosition / 1000)
            if (currentPosition > nextTopTime && nextTopTime > 0 && currentIndex > 0) {
                binding.btnPlay.isSelected = false
                PlayerUtil.pause()
            } else {
                if (nextTopTime < 1 || currentIndex < 1) {
                    initCurrentPosition(currentPosition)
                }
            }
            setSubtitle()
        }
    }

    private fun initCurrentPosition(currentPosition: Int) {
        if (NullUtil.isNotEmpty(list) && PlayerUtil.isPlaying()) {
            for (i in list!!.indices) {
                if (currentPosition < list!![i].time) {
                    if (i - 1 >= 0) {
                        currentIndex = i - 1
                        currentItem = list!![currentIndex]
                        nextTopTime = list!![i].time
                        LogUtil.Log("initCurrentPosition:$currentPosition---nextTopTime:$nextTopTime---currentIndex:$currentIndex")
                        break
                    }
                }
            }
        }
    }

    private fun setSubtitle() {
        if (currentItem != null) {
            val showText = currentItem!!.lyric
            if (showText != binding.content.text.toString().trim { it <= ' ' }) {
                TextHandlerUtil.handlerText(this, binding.content, "$showText ")
            }
        }
    }

    private fun addUnknow() {
        if (currentItem != null) {
            if (!sb.toString().contains(currentItem!!.lyric)) {
                sb.append(currentItem!!.lyric)
                addUnknowText(currentItem, currentIndex)
            }
            if (!PlayerUtil.isPlaying()) {
                playCurrentItem()
            }
        }
    }

    private fun playNext() {
        if (currentItem != null && list!!.size > currentIndex + 1) {
            currentIndex += 1
            playCurrentItem()
        }
    }

    private fun playPrevious() {
        if (currentItem != null && currentIndex > 1) {
            currentIndex -= 1
            playCurrentItem()
        }
    }

    private fun playCurrentItem() {
        if (currentItem != null && NullUtil.isNotEmpty(list) && list!!.size >= currentIndex + 1) {
            if (list!!.size > currentIndex) {
                currentItem = list!![currentIndex]
            }
            nextTopTime = if (list!!.size > currentIndex + 1) {
                list!![currentIndex + 1].time
            } else {
                PlayerUtil.duration.toDouble()
            }
            PlayerUtil.seekTo(currentItem!!.time.toInt())
            PlayerUtil.play()
        }
    }

    private fun addUnknowText(item: utilLrc.Statement?, index: Int) {
        if (item != null) {
            val view =
                LayoutInflater.from(this).inflate(R.layout.reading_lrc_unknow_item, null, false)
            val content = view.findViewById<TextView>(R.id.content)
            val playBtn = view.findViewById<View>(R.id.play_item)
            playBtn.setBackgroundColor(resources.getColor(ColorUtil.getRadomColor()))
            playBtn.tag = index
            TextHandlerUtil.handlerText(this, content, item.lyric + " ")
            playBtn.setOnClickListener {
                currentIndex = playBtn.tag as Int
                playCurrentItem()
            }
            binding.unknowLayout.addView(
                view,
                0,
                ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
            binding.scrollview.scrollTo(0, 0)
        }
    }

    private fun downloadLrc() {
        if (mAVObject != null && !TextUtils.isEmpty(mAVObject!!.lrc_url)) {
            val name = mAVObject!!.object_id + ".lrc"
            val lrcfilepath = SDCardUtil.getDownloadPath(SDCardUtil.lrcPath) + name
            if (!SDCardUtil.isFileExist(lrcfilepath)) {
                LogUtil.Log("downloadLrc:" + mAVObject!!.lrc_url)
                LanguagehelperHttpClient.get(mAVObject!!.lrc_url, object : BgCallback() {
                    override fun onFailured() {}
                    override fun onResponsed(responseString: String) {
                        DownLoadUtil.saveFile(
                            SDCardUtil.lrcPath,
                            name,
                            responseString.toByteArray()
                        )
                        try {
                            val mutilLrc = utilLrc(lrcfilepath)
                            setList(mutilLrc)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                })
            } else {
                try {
                    val mutilLrc = utilLrc(lrcfilepath)
                    setList(mutilLrc)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun setList(mutilLrc: utilLrc?) {
        if (mutilLrc != null) {
            list = mutilLrc.lrcList
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.composition, menu)
        return true
    }

    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        if (menu.size() > 1) {
            setMenuIcon(menu.getItem(1))
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        super.onOptionsItemSelected(item)
        when (item.itemId) {
            R.id.action_share -> copyOrshare(0)
            R.id.action_collected -> {
                setCollectStatus()
                setMenuIcon(item)
                updateData()
            }
        }
        return true
    }

    private fun setCollectStatus() {
        if (TextUtils.isEmpty(mAVObject!!.isCollected)) {
            mAVObject!!.isCollected = "1"
        } else {
            mAVObject!!.isCollected = ""
        }
    }

    private fun updateData() {
        Thread {
            if (mAVObject != null) {
                if (!TextUtils.isEmpty(mAVObject!!.isCollected)) {
                    val cdata = CollectedData()
                    cdata.objectId = mAVObject!!.object_id
                    cdata.name = mAVObject!!.title
                    cdata.type = AVOUtil.Reading.Reading
                    val moshi = Moshi.Builder().build()
                    val jsonAdapter = moshi.adapter(Reading::class.java)
                    cdata.json = jsonAdapter.toJson(mAVObject)
                    insert(cdata)
                } else {
                    val cdata = CollectedData()
                    cdata.objectId = mAVObject!!.object_id
                    remove(cdata)
                }
                LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
            }
        }.start()
    }

    private fun setMenuIcon(item: MenuItem) {
        if (TextUtils.isEmpty(mAVObject!!.isCollected)) {
            item.icon = getDrawable(R.drawable.ic_uncollected_white)
        } else {
            item.icon = getDrawable(R.drawable.ic_collected_white)
        }
    }

    private fun copyOrshare(i: Int) {
        val sb = StringBuilder()
        sb.append(mAVObject!!.title)
        sb.append("\n")
        sb.append(mAVObject!!.content)
        if (i == 0) {
            Setings.share(this, sb.toString())
        } else {
            Setings.copy(this, sb.toString())
        }
    }

    fun onPlayBtnClick() {
        nextTopTime = 0.0
        PlayerUtil.initAndPlay(mAVObject)
    }

    override fun updateUI(music_action: String) {
        if (PlayerUtil.isSameMedia(mAVObject)) {
            if (PlayerService.action_restart == music_action) {
                binding.btnPlay.isSelected = false
                handler!!.removeCallbacks(mRunnable)
            } else if (PlayerService.action_pause == music_action) {
                binding.btnPlay.isSelected = true
                handler!!.postDelayed(mRunnable, 50)
            }
        }
        if (PlayerService.action_loading == music_action) {
            showProgressbar()
        } else if (PlayerService.action_finish_loading == music_action) {
            hideProgressbar()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterBroadcast()
        if (handler != null) {
            handler!!.removeCallbacks(mRunnable)
            handler = null
        }
    }

    override fun onProgressChanged(seekBar: SeekBar, i: Int, b: Boolean) {}
    override fun onStartTrackingTouch(seekBar: SeekBar) {
        handler!!.removeCallbacks(mRunnable)
        PlayerUtil.pause()
    }

    override fun onStopTrackingTouch(seekBar: SeekBar) {
        nextTopTime = 0.0
        PlayerUtil.seekTo(seekBar.progress)
        PlayerUtil.play()
        handler!!.postDelayed(mRunnable, 50)
    }

    private fun guide() {
        if (!mSharedPreferences.getBoolean(KeyUtil.isReadingDetailGuideShow1, false)) {
            val builder = AlertDialog.Builder(this, R.style.Theme_AppCompat_Light_Dialog_Alert)
            builder.setTitle("")
            builder.setMessage("点击英文单词即可查询词意。")
            builder.setPositiveButton("确认", null)
            val dialog = builder.create()
            dialog.show()
            Setings.saveSharedPreferences(
                mSharedPreferences,
                KeyUtil.isReadingDetailGuideShow1,
                true
            )
        }
    }
}