package com.messi.languagehelper.adapter

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.facebook.drawee.view.SimpleDraweeView
import com.jeremyliao.liveeventbus.LiveEventBus
import com.messi.languagehelper.*
import com.messi.languagehelper.bean.BoutiquesBean
import com.messi.languagehelper.box.BoxHelper.delete
import com.messi.languagehelper.box.CollectedData
import com.messi.languagehelper.box.Reading
import com.messi.languagehelper.box.ReadingSubject
import com.messi.languagehelper.util.AVOUtil
import com.messi.languagehelper.util.KSettings
import com.messi.languagehelper.util.KeyUtil
import com.messi.languagehelper.util.PlayerUtil.initAndPlay
import com.messi.languagehelper.util.PlayerUtil.initList
import com.messi.languagehelper.util.PlayerUtil.isPlaying
import com.messi.languagehelper.util.PlayerUtil.isSameMedia
import com.messi.languagehelper.util.Setings
import com.messi.languagehelper.util.ToastUtil.diaplayMesLong
import com.squareup.moshi.Moshi
import java.io.IOException

/**
 * Created by luli on 10/23/16.
 */
class RcCollectedListItemViewHolder(itemView: View, val action: String) :
    RecyclerView.ViewHolder(itemView) {

    private val layout_cover: FrameLayout = itemView.findViewById(R.id.layout_cover)
    private val title: TextView = itemView.findViewById(R.id.title)
    private val type_name: TextView = itemView.findViewById(R.id.type_name)
    private val source_name: TextView = itemView.findViewById(R.id.source_name)
    private val xvideo_more_tv: TextView = itemView.findViewById(R.id.xvideo_more_tv)
    private val music_play_img: ImageView = itemView.findViewById(R.id.music_play_img)
    private val imgs_layout: LinearLayout = itemView.findViewById(R.id.imgs_layout)
    private val normal_layout: LinearLayout = itemView.findViewById(R.id.normal_layout)
    private val xvideo_layout: LinearLayout = itemView.findViewById(R.id.xvideo_layout)
    private val xvideo_content: LinearLayout = itemView.findViewById(R.id.xvideo_content)
    private val list_item_img_parent: FrameLayout = itemView.findViewById(R.id.list_item_img_parent)
    private val ad_layout: FrameLayout = itemView.findViewById(R.id.ad_layout)
    private val item_layout: LinearLayout = itemView.findViewById(R.id.item_layout)
    private val list_item_img: SimpleDraweeView = itemView.findViewById(R.id.list_item_img)
    private val videoplayer_cover: FrameLayout = itemView.findViewById(R.id.videoplayer_cover)
    private val videoplayer_img: SimpleDraweeView = itemView.findViewById(R.id.videoplayer_img)
    private val context: Context = itemView.context

    fun render(mCollectedData: CollectedData) {
        list_item_img_parent.isClickable = false
        ad_layout.visibility = View.GONE
        imgs_layout.visibility = View.GONE
        item_layout.visibility = View.GONE
        videoplayer_cover.visibility = View.GONE
        xvideo_layout.visibility = View.GONE
        title.text = ""
        list_item_img_parent.visibility = View.GONE
        layout_cover.setOnLongClickListener { view: View? ->
            delete(mCollectedData)
            LiveEventBus.get(KeyUtil.UpdateCollectedData).post("")
            diaplayMesLong(context, context.getString(R.string.favorite_cancle))
            true
        }
        val json = mCollectedData.json
        val type = mCollectedData.type
        when {
            AVOUtil.Reading.Reading == type -> {
                val moshi = Moshi.Builder().build()
                val jsonAdapter = moshi.adapter(Reading::class.java)
                val mAVObject = jsonAdapter.fromJson(json)
                if (mAVObject == null) {
                    return
                }
                if (mAVObject.id == null) {
                    mAVObject.id = 0L
                }
                item_layout.visibility = View.VISIBLE
                title.text = mAVObject.title
                type_name.text = mAVObject.type_name
                source_name.text = mAVObject.source_name
                if (mAVObject.type != null && mAVObject.type == "video" && !TextUtils.isEmpty(
                        mAVObject.media_url
                    )
                ) {
                    videoplayer_cover.visibility = View.GONE
                    list_item_img_parent.visibility = View.VISIBLE
                    list_item_img.visibility = View.VISIBLE
                    music_play_img.visibility = View.VISIBLE
                    if (!TextUtils.isEmpty(mAVObject.img_url)) {
                        list_item_img.setImageURI(Uri.parse(mAVObject.img_url))
                    } else {
                        if (mAVObject.img_color > 0) {
                            list_item_img.setImageResource(mAVObject.img_color)
                        } else {
                            list_item_img.setImageResource(R.color.style6_color2)
                        }
                    }
                    if (isSameMedia(mAVObject)) {
                        if (isPlaying()) {
                            music_play_img.setImageResource(R.drawable.jz_click_pause_selector)
                        } else {
                            music_play_img.setImageResource(R.drawable.jz_click_play_selector)
                        }
                    } else {
                        music_play_img.setImageResource(R.drawable.jz_click_play_selector)
                    }
                } else if (mAVObject.type != null && mAVObject.type == "mp3") {
                    videoplayer_cover.visibility = View.GONE
                    list_item_img_parent.visibility = View.VISIBLE
                    list_item_img.visibility = View.VISIBLE
                    music_play_img.visibility = View.VISIBLE
                    if (!TextUtils.isEmpty(mAVObject.img_url)) {
                        list_item_img.setImageURI(mAVObject.img_url)
                    } else {
                        if (mAVObject.img_color > 0) {
                            list_item_img.setImageResource(mAVObject.img_color)
                        } else {
                            list_item_img.setImageResource(R.color.style6_color2)
                        }
                    }
                    if (!TextUtils.isEmpty(mAVObject.media_url)) {
                        music_play_img.visibility = View.VISIBLE
                        if (isSameMedia(mAVObject)) {
                            if (isPlaying()) {
                                music_play_img.setImageResource(R.drawable.jz_click_pause_selector)
                            } else {
                                music_play_img.setImageResource(R.drawable.jz_click_play_selector)
                            }
                        } else {
                            music_play_img.setImageResource(R.drawable.jz_click_play_selector)
                        }
                    } else {
                        music_play_img.visibility = View.GONE
                    }
                    list_item_img_parent.isClickable = true
                    list_item_img_parent.setOnClickListener {
                        initAndPlay(
                            mAVObject,
                            true,
                            0
                        )
                    }
                } else {
                    videoplayer_cover.visibility = View.GONE
                    music_play_img.visibility = View.GONE
                    if (!TextUtils.isEmpty(mAVObject.img_url)) {
                        list_item_img_parent.visibility = View.VISIBLE
                        list_item_img.visibility = View.VISIBLE
                        list_item_img.setImageURI(Uri.parse(mAVObject.img_url))
                    } else {
                        list_item_img_parent.visibility = View.GONE
                        list_item_img.visibility = View.GONE
                    }
                }
                layout_cover.setOnClickListener { view: View? -> toDetailActivity(type, mAVObject) }
            }
            AVOUtil.Boutiques.Boutiques == type -> {
                try {
                    val moshi = Moshi.Builder().build()
                    val jsonAdapter = moshi.adapter(
                        BoutiquesBean::class.java
                    )
                    val mBoutiquesBean = jsonAdapter.fromJson(json)
                    item_layout.visibility = View.GONE
                    title.text = mBoutiquesBean!!.title
                    type_name.text = mBoutiquesBean.tag
                    source_name.text = mBoutiquesBean.source_name
                    videoplayer_cover.visibility = View.VISIBLE
                    list_item_img_parent.visibility = View.GONE
                    list_item_img.visibility = View.VISIBLE
                    music_play_img.visibility = View.VISIBLE
                    videoplayer_img.setImageURI(mBoutiquesBean.img_url)
                    layout_cover.setOnClickListener { view: View? ->
                        toDetailActivity(
                            type,
                            mBoutiquesBean
                        )
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            AVOUtil.SubjectList.SubjectList == type -> {
                val moshi = Moshi.Builder().build()
                val jsonAdapter = moshi.adapter(ReadingSubject::class.java)
                val mReadingSubject = jsonAdapter.fromJson(json)
                if (mReadingSubject == null) {
                    return
                }
                if (TextUtils.isEmpty(mReadingSubject.order)) {
                    mReadingSubject.order = KSettings.OrderAsc
                }
                item_layout.visibility = View.VISIBLE
                title.text = mReadingSubject.name
                type_name.text = mReadingSubject.source_name
                source_name.text = Setings.getCategoryName(mReadingSubject.category)
                videoplayer_cover.visibility = View.GONE
                list_item_img_parent.visibility = View.VISIBLE
                list_item_img.visibility = View.VISIBLE
                music_play_img.visibility = View.GONE
                if (!TextUtils.isEmpty(mReadingSubject.img)) {
                    list_item_img.setImageURI(mReadingSubject.img)
                } else {
                    list_item_img.setImageResource(mReadingSubject.imgId)
                }
                layout_cover.setOnClickListener { view: View? ->
                    toDetailActivity(
                        type,
                        mReadingSubject
                    )
                }
            }
        }
    }

    private fun toDetailActivity(type: String, `object`: Any?) {
        val intent = Intent()
        var toDetail: Class<*>? = null
        if (AVOUtil.Reading.Reading == type) {
            val item = `object` as Reading
            val datas = ArrayList<Reading>()
            datas.add(item)
            if (!TextUtils.isEmpty(item.type) && "video" == item.type) {
                intent.putExtra(KeyUtil.ParcelableData, item)
                toDetail = ReadVideoActivity::class.java
            } else if (!TextUtils.isEmpty(item.type) && "mp3" == item.type) {
                Setings.dataMap[KeyUtil.DataMapKey] = datas
                intent.putExtra(KeyUtil.IndexKey, 0)
                initList(datas, 0)
                if (TextUtils.isEmpty(action)) {
                    toDetail = PlayListActivity::class.java
                } else {
                    LiveEventBus.get(KeyUtil.PlayHistory).post("show")
                }
            } else {
                Setings.dataMap[KeyUtil.DataMapKey] = datas
                intent.putExtra(KeyUtil.IndexKey, 0)
                toDetail = ReadingDetailActivity::class.java
            }
        } else if (AVOUtil.Boutiques.Boutiques == type) {
            val mBoutiquesBean = `object` as BoutiquesBean?
            mBoutiquesBean?.let {
                toDetail = ReadVideoActivity::class.java
                intent.putExtra(KeyUtil.BoutiqueCode, mBoutiquesBean.code)
                intent.putExtra(KeyUtil.ObjectKey, mBoutiquesBean)
            }
        } else if (AVOUtil.SubjectList.SubjectList == type) {
            val mReadingSubject = `object` as ReadingSubject?
            toDetail = AlbumDetailListActivity::class.java
            intent.putExtra(KeyUtil.ActionbarTitle, mReadingSubject!!.name)
            intent.putExtra(KeyUtil.SubjectName, mReadingSubject.name)
            intent.putExtra(KeyUtil.ObjectKey, mReadingSubject)
        }
        if (toDetail != null) {
            intent.setClass(context, toDetail!!)
            context.startActivity(intent)
        }
    }

}